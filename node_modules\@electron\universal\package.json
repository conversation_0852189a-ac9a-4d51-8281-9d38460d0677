{"name": "@electron/universal", "version": "1.5.1", "description": "Utility for creating Universal macOS applications from two x64 and arm64 Electron applications", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "license": "MIT", "keywords": ["electron", "apple silicon", "universal"], "repository": {"type": "git", "url": "https://github.com/electron/universal.git"}, "engines": {"node": ">=8.6"}, "files": ["dist/*", "entry-asar/*", "!entry-asar/**/*.ts", "README.md"], "author": "<PERSON>", "scripts": {"build": "tsc -p tsconfig.cjs.json && tsc -p tsconfig.esm.json && tsc -p tsconfig.entry-asar.json", "lint": "prettier --check \"{src,entry-asar}/**/*.ts\"", "prettier:write": "prettier --write \"{src,entry-asar}/**/*.ts\"", "prepublishOnly": "npm run build", "test": "exit 0", "prepare": "husky install"}, "devDependencies": {"@continuous-auth/semantic-release-npm": "^3.0.0", "@types/debug": "^4.1.5", "@types/fs-extra": "^9.0.4", "@types/minimatch": "^3.0.5", "@types/node": "^14.14.7", "@types/plist": "^3.0.2", "husky": "^8.0.0", "lint-staged": "^10.5.1", "prettier": "^2.1.2", "typescript": "^4.0.5"}, "dependencies": {"@electron/asar": "^3.2.1", "@malept/cross-spawn-promise": "^1.1.0", "debug": "^4.3.1", "dir-compare": "^3.0.0", "fs-extra": "^9.0.1", "minimatch": "^3.0.4", "plist": "^3.0.4"}, "lint-staged": {"*.ts": ["prettier --write"]}}