{"version": 3, "file": "mention-match.js", "sourceRoot": "", "sources": ["../../../src/match/mention-match.ts"], "names": [], "mappings": ";;;;AAAA,iCAA6C;AAG7C;;;;;;;GAOG;AACH;IAAkC,6CAAK;IAgBnC;;;;OAIG;IACH,sBAAY,GAAuB;QAAnC,YACI,kBAAM,GAAG,CAAC,SAIb;QAzBD;;;;;WAKG;QACc,iBAAW,GAAoB,SAAS,CAAC,CAAC,gGAAgG;QAE3J;;;;WAIG;QACc,aAAO,GAAW,EAAE,CAAC,CAAC,gGAAgG;QAUnI,KAAI,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC3B,KAAI,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;;IACvC,CAAC;IAED;;;;;OAKG;IACH,8BAAO,GAAP;QACI,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACH,iCAAU,GAAV;QACI,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACH,qCAAc,GAAd;QACI,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,oCAAa,GAAb;QACI,QAAQ,IAAI,CAAC,WAAW,EAAE;YACtB,KAAK,SAAS;gBACV,OAAO,sBAAsB,GAAG,IAAI,CAAC,OAAO,CAAC;YACjD,KAAK,WAAW;gBACZ,OAAO,wBAAwB,GAAG,IAAI,CAAC,OAAO,CAAC;YACnD,KAAK,YAAY;gBACb,OAAO,yBAAyB,GAAG,IAAI,CAAC,OAAO,CAAC;YACpD,KAAK,QAAQ;gBACT,OAAO,0BAA0B,GAAG,IAAI,CAAC,OAAO,CAAC;YAErD;gBACI,uGAAuG;gBACvG,MAAM,IAAI,KAAK,CAAC,4CAA4C,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;SACxF;IACL,CAAC;IAED;;;;OAIG;IACH,oCAAa,GAAb;QACI,OAAO,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACH,0CAAmB,GAAnB;QACI,IAAI,gBAAgB,GAAG,iBAAM,mBAAmB,WAAE,EAC9C,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAExC,IAAI,WAAW,EAAE;YACb,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACtC;QACD,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IACL,mBAAC;AAAD,CAAC,AAxGD,CAAkC,aAAK,GAwGtC;AAxGY,oCAAY"}