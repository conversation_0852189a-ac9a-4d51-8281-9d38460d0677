<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>McDonald's Restaurant Query Tool</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  <link rel="stylesheet" href="./styles.css">
</head>
<body>
  <div class="container-fluid">
    <!-- Header -->
    <header class="app-header">
      <h1>
        <img src="./assets/logo.png" alt="McDonald's" class="app-logo">
        Restaurant Query Tool
      </h1>
    </header>

    <!-- Main Content -->
    <div class="row">
      <!-- Left Sidebar - Restaurant Selection -->
      <div class="col-md-3">
        <div class="card">
          <div class="card-header bg-primary">
            <h5 class="mb-0">Restaurants</h5>
          </div>
          <div class="card-body">
            <div class="restaurant-search mb-3">
              <input type="text" class="form-control" id="restaurant-search" placeholder="Search restaurants...">
            </div>

            <div class="d-flex justify-content-between mb-2">
              <button id="select-all" class="btn btn-sm btn-outline-primary">Select All</button>
              <button id="deselect-all" class="btn btn-sm btn-outline-secondary">Deselect All</button>
              <span class="badge bg-primary" id="selected-count">0</span>
            </div>

            <div id="restaurant-list" class="restaurant-container">
              <div class="d-flex justify-content-center align-items-center h-100">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <span class="ms-2">Loading restaurants...</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Area -->
      <div class="col-md-9">
        <!-- Tabs -->
        <ul class="nav nav-tabs mb-3" id="main-tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="tab-query" data-bs-toggle="tab" data-bs-target="#query-tab" type="button" role="tab" aria-controls="query-tab" aria-selected="true">
              <i class="bi bi-search"></i> Query
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="tab-management" data-bs-toggle="tab" data-bs-target="#management-tab" type="button" role="tab" aria-controls="management-tab" aria-selected="false">
              <i class="bi bi-building"></i> Management
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="tab-deployment" data-bs-toggle="tab" data-bs-target="#deployment-tab" type="button" role="tab" aria-controls="deployment-tab" aria-selected="false">
              <i class="bi bi-cloud-upload"></i> Deployment
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="tab-results" data-bs-toggle="tab" data-bs-target="#results-tab" type="button" role="tab" aria-controls="results-tab" aria-selected="false">
              <i class="bi bi-table"></i> Results
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="tab-progress" data-bs-toggle="tab" data-bs-target="#progress-tab" type="button" role="tab" aria-controls="progress-tab" aria-selected="false">
              <i class="bi bi-activity"></i> Progress
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="tab-history" data-bs-toggle="tab" data-bs-target="#history-tab" type="button" role="tab" aria-controls="history-tab" aria-selected="false">
              <i class="bi bi-clock-history"></i> History
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="tab-settings" data-bs-toggle="tab" data-bs-target="#settings-tab" type="button" role="tab" aria-controls="settings-tab" aria-selected="false">
              <i class="bi bi-gear"></i> Settings
            </button>
          </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="main-tabs-content">
          <!-- Query Tab -->
          <div class="tab-pane fade show active" id="query-tab" role="tabpanel" aria-labelledby="tab-query">
            <div class="card">
              <div class="card-header bg-primary">
                <h5 class="mb-0">Query Parameters</h5>
              </div>
              <div class="card-body">
                <form id="query-form">
                  <div class="row mb-3">
                    <div class="col-md-6">
                      <label for="start-date" class="form-label">Start Date</label>
                      <input type="datetime-local" class="form-control" id="start-date">
                    </div>
                    <div class="col-md-6">
                      <label for="end-date" class="form-label">End Date</label>
                      <input type="datetime-local" class="form-control" id="end-date">
                    </div>
                  </div>

                  <div class="mb-3">
                    <label for="custom-query" class="form-label">Custom Query</label>
                    <textarea class="form-control" id="custom-query" rows="4" placeholder="e.g., from SalesDatas where SaleTime between '2023-01-01' and '2023-01-31' limit 50"></textarea>
                    <div class="form-text">Leave empty for default query</div>
                  </div>

                  <button type="submit" class="btn btn-primary" id="execute-button">
                    <i class="bi bi-play-fill"></i> Execute Query
                  </button>
                </form>
              </div>
            </div>
          </div>

          <!-- Restaurant Management Tab -->
          <div class="tab-pane fade" id="management-tab" role="tabpanel" aria-labelledby="tab-management">
            <div class="row">
              <!-- Restaurant Groups -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-primary">
                    <h5 class="mb-0">Restaurant Groups</h5>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <div class="input-group">
                        <input type="text" class="form-control" id="new-group-name" placeholder="Group name...">
                        <button class="btn btn-outline-primary" type="button" id="create-group">
                          <i class="bi bi-plus"></i> Create Group
                        </button>
                      </div>
                    </div>

                    <div id="restaurant-groups" class="groups-container">
                      <div class="empty-state">
                        <div class="empty-state-icon">📁</div>
                        <p>No groups created yet</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Group Management -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-primary">
                    <h5 class="mb-0">Group Details</h5>
                  </div>
                  <div class="card-body">
                    <div id="group-details">
                      <div class="empty-state">
                        <div class="empty-state-icon">📋</div>
                        <p>Select a group to view details</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Bulk Operations -->
            <div class="row mt-3">
              <div class="col-12">
                <div class="card">
                  <div class="card-header bg-primary">
                    <h5 class="mb-0">Bulk Operations</h5>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-4">
                        <button class="btn btn-outline-success w-100" id="test-all-connections">
                          <i class="bi bi-wifi"></i> Test All Connections
                        </button>
                      </div>
                      <div class="col-md-4">
                        <button class="btn btn-outline-warning w-100" id="deploy-to-selected">
                          <i class="bi bi-cloud-upload"></i> Deploy to Selected
                        </button>
                      </div>
                      <div class="col-md-4">
                        <button class="btn btn-outline-info w-100" id="export-restaurant-config">
                          <i class="bi bi-download"></i> Export Configuration
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Deployment Tab -->
          <div class="tab-pane fade" id="deployment-tab" role="tabpanel" aria-labelledby="tab-deployment">
            <div class="row">
              <!-- Deployment Configuration -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-primary">
                    <h5 class="mb-0">Deployment Configuration</h5>
                  </div>
                  <div class="card-body">
                    <form id="deployment-form">
                      <div class="mb-3">
                        <label for="deployment-path" class="form-label">Target Path</label>
                        <input type="text" class="form-control" id="deployment-path"
                               value="D:\Newpos61\Support_Tools\Ultimate_support_tools" readonly>
                        <div class="form-text">Standard deployment path for all restaurants</div>
                      </div>

                      <div class="mb-3">
                        <label for="deployment-username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="deployment-username" value="Administrator">
                      </div>

                      <div class="mb-3">
                        <label for="deployment-password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="deployment-password" value="Jvr963*14">
                      </div>

                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="create-ultimate-folder" checked>
                          <label class="form-check-label" for="create-ultimate-folder">
                            Create Ultimate folder if it doesn't exist
                          </label>
                        </div>
                      </div>

                      <div class="mb-3">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="backup-existing" checked>
                          <label class="form-check-label" for="backup-existing">
                            Backup existing files before deployment
                          </label>
                        </div>
                      </div>
                    </form>
                  </div>
                </div>
              </div>

              <!-- Deployment Actions -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-primary">
                    <h5 class="mb-0">Deployment Actions</h5>
                  </div>
                  <div class="card-body">
                    <div class="d-grid gap-2">
                      <button class="btn btn-success" id="deploy-script">
                        <i class="bi bi-cloud-upload"></i> Deploy Script to Selected Restaurants
                      </button>

                      <button class="btn btn-info" id="check-deployment-status">
                        <i class="bi bi-search"></i> Check Deployment Status
                      </button>

                      <button class="btn btn-warning" id="update-existing-deployments">
                        <i class="bi bi-arrow-repeat"></i> Update Existing Deployments
                      </button>

                      <button class="btn btn-danger" id="remove-deployments">
                        <i class="bi bi-trash"></i> Remove Deployments
                      </button>
                    </div>

                    <hr>

                    <div class="deployment-stats">
                      <h6>Deployment Statistics</h6>
                      <div class="row text-center">
                        <div class="col-4">
                          <div class="stat-item">
                            <div class="stat-number" id="deployed-count">0</div>
                            <div class="stat-label">Deployed</div>
                          </div>
                        </div>
                        <div class="col-4">
                          <div class="stat-item">
                            <div class="stat-number" id="pending-count">0</div>
                            <div class="stat-label">Pending</div>
                          </div>
                        </div>
                        <div class="col-4">
                          <div class="stat-item">
                            <div class="stat-number" id="failed-count">0</div>
                            <div class="stat-label">Failed</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Deployment Status -->
            <div class="row mt-3">
              <div class="col-12">
                <div class="card">
                  <div class="card-header bg-primary">
                    <h5 class="mb-0">Deployment Status</h5>
                  </div>
                  <div class="card-body">
                    <div id="deployment-status-list">
                      <div class="empty-state">
                        <div class="empty-state-icon">📦</div>
                        <p>No deployments yet</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Results Tab -->
          <div class="tab-pane fade" id="results-tab" role="tabpanel" aria-labelledby="tab-results">
            <div class="card">
              <div class="card-header bg-primary d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Query Results</h5>
                <div>
                  <button id="export-json" class="btn btn-sm btn-light me-1" data-bs-toggle="tooltip" title="Export to JSON">
                    <i class="bi bi-file-earmark-code"></i> JSON
                  </button>
                  <button id="export-csv" class="btn btn-sm btn-light" data-bs-toggle="tooltip" title="Export to CSV">
                    <i class="bi bi-file-earmark-spreadsheet"></i> CSV
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div id="results-container" class="results-container">
                  <div class="empty-state">
                    <div class="empty-state-icon">📊</div>
                    <p>No results yet. Execute a query to see results here.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Progress Tab -->
          <div class="tab-pane fade" id="progress-tab" role="tabpanel" aria-labelledby="tab-progress">
            <div class="card">
              <div class="card-header bg-primary">
                <h5 class="mb-0">Query Progress</h5>
              </div>
              <div class="card-body">
                <div id="progress-list">
                  <div class="empty-state">
                    <div class="empty-state-icon">⏱️</div>
                    <p>No queries running</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- History Tab -->
          <div class="tab-pane fade" id="history-tab" role="tabpanel" aria-labelledby="tab-history">
            <div class="card">
              <div class="card-header bg-primary">
                <h5 class="mb-0">Query History</h5>
              </div>
              <div class="card-body">
                <div id="query-history-list">
                  <div class="empty-state">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Loading history...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Settings Tab -->
          <div class="tab-pane fade" id="settings-tab" role="tabpanel" aria-labelledby="tab-settings">
            <div class="row">
              <!-- General Settings -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-primary">
                    <h5 class="mb-0">General Settings</h5>
                  </div>
                  <div class="card-body">
                    <form id="settings-form">
                      <div class="settings-item">
                        <label for="results-limit" class="form-label">Default Results Limit</label>
                        <input type="number" class="form-control" id="results-limit" min="1" max="1000" value="20">
                        <div class="form-text">Maximum number of records to return per query</div>
                      </div>

                      <div class="settings-item">
                        <label for="results-location" class="form-label">Results Location</label>
                        <div class="input-group">
                          <input type="text" class="form-control" id="results-location" value="Results">
                          <button class="btn btn-outline-secondary" type="button" id="browse-results-location">Browse...</button>
                        </div>
                        <div class="form-text">Directory where query results will be saved</div>
                      </div>

                      <div class="settings-item">
                        <label for="query-timeout" class="form-label">Query Timeout (seconds)</label>
                        <input type="number" class="form-control" id="query-timeout" min="10" max="300" value="30">
                        <div class="form-text">Maximum time to wait for query execution</div>
                      </div>

                      <div class="settings-item">
                        <label for="max-concurrent" class="form-label">Max Concurrent Queries</label>
                        <input type="number" class="form-control" id="max-concurrent" min="1" max="10" value="3">
                        <div class="form-text">Maximum number of simultaneous queries</div>
                      </div>

                      <button type="submit" class="btn btn-primary mt-3">Save Settings</button>
                    </form>
                  </div>
                </div>
              </div>

              <!-- Connection Settings -->
              <div class="col-md-6">
                <div class="card">
                  <div class="card-header bg-primary">
                    <h5 class="mb-0">Connection Settings</h5>
                  </div>
                  <div class="card-body">
                    <form id="connection-settings-form">
                      <div class="settings-item">
                        <label for="default-username" class="form-label">Default Username</label>
                        <input type="text" class="form-control" id="default-username" value="Administrator">
                        <div class="form-text">Default username for remote connections</div>
                      </div>

                      <div class="settings-item">
                        <label for="default-password" class="form-label">Default Password</label>
                        <input type="password" class="form-control" id="default-password" value="Jvr963*14">
                        <div class="form-text">Default password for remote connections</div>
                      </div>

                      <div class="settings-item">
                        <label for="connection-timeout" class="form-label">Connection Timeout (seconds)</label>
                        <input type="number" class="form-control" id="connection-timeout" min="5" max="60" value="15">
                        <div class="form-text">Maximum time to wait for connection</div>
                      </div>

                      <div class="settings-item">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="auto-retry" checked>
                          <label class="form-check-label" for="auto-retry">
                            Auto-retry failed connections
                          </label>
                        </div>
                        <div class="form-text">Automatically retry failed connections up to 3 times</div>
                      </div>

                      <button type="submit" class="btn btn-primary mt-3">Save Connection Settings</button>
                    </form>
                  </div>
                </div>
              </div>
            </div>

            <!-- Advanced Settings -->
            <div class="row mt-3">
              <div class="col-12">
                <div class="card">
                  <div class="card-header bg-primary">
                    <h5 class="mb-0">Advanced Settings</h5>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-6">
                        <div class="settings-item">
                          <label for="log-level" class="form-label">Log Level</label>
                          <select class="form-control" id="log-level">
                            <option value="error">Error</option>
                            <option value="warn">Warning</option>
                            <option value="info" selected>Info</option>
                            <option value="debug">Debug</option>
                          </select>
                          <div class="form-text">Level of detail for application logs</div>
                        </div>
                      </div>

                      <div class="col-md-6">
                        <div class="settings-item">
                          <label for="backup-retention" class="form-label">Backup Retention (days)</label>
                          <input type="number" class="form-control" id="backup-retention" min="1" max="365" value="30">
                          <div class="form-text">How long to keep backup files</div>
                        </div>
                      </div>
                    </div>

                    <div class="row mt-3">
                      <div class="col-md-6">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="enable-notifications" checked>
                          <label class="form-check-label" for="enable-notifications">
                            Enable desktop notifications
                          </label>
                        </div>
                      </div>

                      <div class="col-md-6">
                        <div class="form-check">
                          <input class="form-check-input" type="checkbox" id="auto-save-results" checked>
                          <label class="form-check-label" for="auto-save-results">
                            Auto-save query results
                          </label>
                        </div>
                      </div>
                    </div>

                    <div class="mt-3">
                      <button class="btn btn-warning" id="reset-settings">
                        <i class="bi bi-arrow-clockwise"></i> Reset to Defaults
                      </button>
                      <button class="btn btn-info ms-2" id="export-settings">
                        <i class="bi bi-download"></i> Export Settings
                      </button>
                      <button class="btn btn-secondary ms-2" id="import-settings">
                        <i class="bi bi-upload"></i> Import Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap & App Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="./app.js"></script>
</body>
</html>