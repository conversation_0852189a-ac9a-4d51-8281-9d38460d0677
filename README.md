# McDonald's Restaurant Query Tool - Enhanced Version

A comprehensive Electron-based application for managing and querying McDonald's restaurant systems with advanced deployment and management features.

## 🚀 New Features

### 📊 Enhanced Tabs System
- **Query Tab**: Execute queries with deployment integration
- **Management Tab**: Organize restaurants into groups and manage connections
- **Deployment Tab**: Deploy and manage scripts across restaurant networks
- **Results Tab**: Enhanced results visualization with detailed analytics
- **Progress Tab**: Real-time progress tracking for all operations
- **History Tab**: Query history with advanced filtering
- **Settings Tab**: Comprehensive configuration management

### 🏢 Restaurant Management
- **Restaurant Groups**: Create and manage restaurant groups for easier organization
- **Bulk Operations**: Test connections, deploy scripts, and execute queries on multiple restaurants
- **Connection Status**: Real-time connection monitoring with visual indicators
- **Group Selection**: Quickly select all restaurants in a group for operations

### 🚀 Deployment System
- **Automated Deployment**: Deploy `option1_query.bat` to `D:\Newpos61\Support_Tools\Ultimate_support_tools`
- **Deployment Status Tracking**: Monitor deployment status across all restaurants
- **Backup Management**: Automatic backup of existing files before deployment
- **Remote Execution**: Execute deployed scripts remotely on restaurant servers
- **Deployment Statistics**: Visual dashboard showing deployment status

### 🔧 Advanced Settings
- **Connection Settings**: Configure default credentials and timeouts
- **Query Settings**: Set query timeouts and concurrent execution limits
- **Backup Settings**: Configure backup retention and automatic cleanup
- **Import/Export**: Backup and restore all settings and configurations

## Prerequisites

- Windows 10/11
- Node.js 16 or higher
- PowerShell with administrative privileges
- Network access to restaurant servers
- Valid credentials for restaurant systems
- Required files in the application directory:
  - `Restaurants.txt` - List of restaurants with their IDs, names, and IP addresses
  - `option1_query.bat` - Script for executing queries

## 🛠️ Installation

1. Clone or download this repository
2. Navigate to the project directory
3. Install dependencies:

```bash
npm install
```

## Running the Application

To start the application:

```bash
npm start
```

For development with hot reloading:

```bash
npm run dev
```

## Building the Application

To build the application for distribution:

```bash
npm run build
```

This will create executable files in the `dist` directory.

## 📖 Usage Guide

### Initial Setup

1. **Configure Settings**: Go to Settings tab and configure:
   - Default username/password for restaurant connections
   - Query timeouts and limits
   - Results location and backup settings

2. **Load Restaurants**: The application will automatically load restaurants from `Restaurants.txt`

3. **Create Groups**: Use the Management tab to organize restaurants into logical groups

### Deployment Workflow

1. **Select Restaurants**: Choose restaurants for deployment
2. **Configure Deployment**: Set deployment path and credentials in Deployment tab
3. **Deploy Script**: Click "Deploy Script to Selected Restaurants"
4. **Monitor Progress**: Watch real-time deployment status
5. **Verify Deployment**: Use "Check Deployment Status" to confirm successful deployment

### Query Execution

1. **Select Restaurants**: Choose restaurants or select entire groups
2. **Configure Query**: Set date ranges and custom SQL queries
3. **Execute**: The system will automatically check deployment status and offer to deploy if needed
4. **Monitor Progress**: Track query execution in real-time
5. **View Results**: Analyze results with enhanced visualization tools

## Project Structure

```
├── src/
│   ├── assets/           # Application assets (icons, images)
│   ├── main/             # Electron main process files
│   │   └── main.js       # Main application entry point
│   └── renderer/         # Renderer process files
│       ├── css/          # Stylesheets
│       ├── js/           # JavaScript files
│       └── index.html    # Main application HTML
├── docs/                 # Documentation
├── scripts/              # Utility scripts
├── Restaurants.txt       # Restaurant data file
├── option1_query.bat     # Query execution script
└── package.json          # Project configuration
```

## Usage Instructions

1. **Restaurant Selection**
   - The left panel displays all available restaurants from Restaurants.txt
   - Use the search box to filter restaurants by name, ID, or IP
   - Click on individual restaurants to select/deselect them
   - Use "Select All" or "Deselect All" buttons for bulk selection

2. **Query Parameters**
   - Specify a start date and end date for time-based queries
   - Enter a custom RavenDB query or leave blank for the default query

3. **Executing Queries**
   - Click "Execute Query" to run the query on selected restaurants
   - The progress tab shows real-time status for each restaurant

4. **Viewing Results**
   - Results are displayed in the Results tab in tabular format
   - For each restaurant, you'll see SaleTime and TenderPos data
   - Click "View Details" to see complete information for a restaurant

5. **Exporting Results**
   - Click "JSON" or "CSV" buttons to export the current results
   - Files are saved with a timestamp in the selected location

6. **Query History**
   - The History tab shows previously executed queries
   - Click on a history item to load its parameters

7. **Settings**
   - The Settings tab allows customizing application behavior
   - Set default result limits and storage locations

## Troubleshooting

- If a restaurant query fails, check:
  - Network connectivity to the server
  - PowerShell remoting is enabled on the target server
  - The RavenDB service is running
  - Credentials have the necessary permissions

## License

This project is for internal use only.

## Support

For support or feature requests, please contact the McDonald's IT support team.