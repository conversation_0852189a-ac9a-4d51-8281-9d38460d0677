{"version": 3, "file": "anchor-tag-builder.js", "sourceRoot": "", "sources": ["../../src/anchor-tag-builder.ts"], "names": [], "mappings": ";;;AACA,uCAAqC;AAErC,4DAA0D;AAC1D,8DAA4D;AAC5D,wDAAsD;AAEtD;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH;IAmBI;;;OAGG;IACH,0BAAY,GAA6B;QAA7B,oBAAA,EAAA,QAA6B;QAtBzC;;;WAGG;QACc,cAAS,GAAY,KAAK,CAAC,CAAC,gGAAgG;QAE7I;;;WAGG;QACc,aAAQ,GAAsB,EAAE,CAAC,CAAC,gGAAgG;QAEnJ;;;WAGG;QACc,cAAS,GAAW,EAAE,CAAC,CAAC,gGAAgG;QAOrI,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC;QACxC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,IAAI,EAAE,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC;IACzC,CAAC;IAED;;;;;;;OAOG;IACH,gCAAK,GAAL,UAAM,KAAY;QACd,OAAO,IAAI,kBAAO,CAAC;YACf,OAAO,EAAE,GAAG;YACZ,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YAC9B,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;SAC3D,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG;IACO,sCAAW,GAArB,UAAsB,KAAY;QAC9B,IAAI,KAAK,GAAmC;YACxC,IAAI,EAAE,KAAK,CAAC,aAAa,EAAE,EAAE,yCAAyC;SACzE,CAAC;QAEF,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,QAAQ,EAAE;YACV,KAAK,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAC;SAC7B;QACD,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;YAC3B,KAAK,CAAC,KAAK,CAAC,GAAG,qBAAqB,CAAC,CAAC,gEAAgE;SACzG;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE;gBAC7E,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;aAC1C;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;;;;;;;;;OAmBG;IACO,yCAAc,GAAxB,UAAyB,KAAY;QACjC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAE/B,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,EAAE,CAAC;SACb;aAAM;YACH,IAAI,aAAa,GAAG,CAAC,SAAS,CAAC,EAC3B,gBAAgB,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAEnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBACzD,aAAa,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;aAC7D;YACD,OAAO,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClC;IACL,CAAC;IAED;;;;;;;;OAQG;IACK,4CAAiB,GAAzB,UAA0B,UAAkB;QACxC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAEzC,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;;;;;;OAUG;IACK,qCAAU,GAAlB,UAAmB,UAAkB;QACjC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7B,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM;YAAE,OAAO,UAAU,CAAC;QAErD,IAAI,cAAc,GAAG,QAAQ,CAAC,MAAM,EAChC,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC;QAEzC,IAAI,gBAAgB,KAAK,OAAO,EAAE;YAC9B,OAAO,IAAA,8BAAa,EAAC,UAAU,EAAE,cAAc,CAAC,CAAC;SACpD;aAAM,IAAI,gBAAgB,KAAK,QAAQ,EAAE;YACtC,OAAO,IAAA,gCAAc,EAAC,UAAU,EAAE,cAAc,CAAC,CAAC;SACrD;aAAM;YACH,OAAO,IAAA,0BAAW,EAAC,UAAU,EAAE,cAAc,CAAC,CAAC;SAClD;IACL,CAAC;IACL,uBAAC;AAAD,CAAC,AA1JD,IA0JC;AA1JY,4CAAgB"}