// Restaurant Query Tool - Main Application Script
const { ipc<PERSON>enderer } = require('electron');
const moment = require('moment');
const _ = require('lodash');

// Global state
let allRestaurants = [];
let selectedRestaurants = [];
let queryResults = [];
let config = {};
let isExecutingQuery = false;
let restaurantGroups = [];
let selectedGroup = null;
let deploymentStatus = {};
let connectionStatus = {};

// DOM Ready
document.addEventListener('DOMContentLoaded', async () => {
  console.log('Application starting...');

  // Show immediate feedback that the app is loading
  showToast('Application starting...', 'info');

  try {
    // Initialize UI components
    initializeTooltips();

    // Load configuration
    await loadConfig();

    // Load restaurants
    await loadRestaurants();

    // Set up event listeners
    setupEventListeners();

    // Load query history
    loadQueryHistory();

    // Load restaurant groups
    loadRestaurantGroups();

    // Load deployment status
    loadDeploymentStatus();

    // Set up tabs
    setupTabs();

    console.log('Application initialized');
    showToast('Application ready!', 'success');
  } catch (error) {
    console.error('Error initializing application:', error);
    showToast('Error initializing application: ' + error.message, 'danger');
  }
});

/**
 * Initialize Bootstrap tooltips
 */
function initializeTooltips() {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
}

/**
 * Load application configuration
 */
async function loadConfig() {
  try {
    config = await ipcRenderer.invoke('get-config');

    // Apply configuration
    if (config.defaultResultsLimit) {
      document.getElementById('results-limit').value = config.defaultResultsLimit;
    }

    if (config.resultFileLocation) {
      document.getElementById('results-location').value = config.resultFileLocation;
    }
  } catch (error) {
    console.error('Error loading configuration:', error);
    showToast('Error loading configuration', 'danger');
  }
}

/**
 * Load restaurants from file
 */
async function loadRestaurants() {
  try {
    console.log('Loading restaurants...');
    const result = await ipcRenderer.invoke('read-restaurants');
    console.log('Restaurant load result:', result);

    if (result.error) {
      console.error('Restaurant load error:', result.error);
      showErrorState(result.error);
      return;
    }

    allRestaurants = result.restaurants;
    console.log('Loaded restaurants:', allRestaurants.length);

    // Temporary alert to check if restaurants are loading
    if (allRestaurants.length > 0) {
      showToast(`Loaded ${allRestaurants.length} restaurants successfully!`, 'success');
    }

    displayRestaurants(allRestaurants);
  } catch (error) {
    console.error('Error loading restaurants:', error);
    showErrorState('Error loading restaurants. Check console for details.');
  }
}

/**
 * Display restaurants in the UI
 */
function displayRestaurants(restaurants) {
  const restaurantList = document.getElementById('restaurant-list');
  restaurantList.innerHTML = '';

  if (restaurants.length === 0) {
    restaurantList.innerHTML = '<div class="empty-state"><div class="empty-state-icon">📋</div><p>No restaurants found</p></div>';
    return;
  }

  restaurants.forEach(restaurant => {
    const item = document.createElement('div');
    item.className = 'restaurant-item';
    item.dataset.id = restaurant.id;
    item.dataset.ip = restaurant.ip;

    item.innerHTML = `
      <div>
        <div class="restaurant-name">${restaurant.name}</div>
        <div class="restaurant-id">ID: ${restaurant.id}</div>
      </div>
      <div class="restaurant-ip">${restaurant.ip}</div>
    `;

    item.addEventListener('click', toggleRestaurantSelection);
    restaurantList.appendChild(item);
  });
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
  // Restaurant selection buttons
  document.getElementById('select-all').addEventListener('click', selectAllRestaurants);
  document.getElementById('deselect-all').addEventListener('click', deselectAllRestaurants);

  // Restaurant search
  document.getElementById('restaurant-search').addEventListener('input', filterRestaurants);

  // Query form
  document.getElementById('query-form').addEventListener('submit', executeQuery);

  // Export results
  document.getElementById('export-json').addEventListener('click', exportResults);
  document.getElementById('export-csv').addEventListener('click', exportResultsCSV);

  // Settings form
  document.getElementById('settings-form').addEventListener('submit', saveSettings);
  document.getElementById('browse-results-location').addEventListener('click', browseResultsLocation);
  document.getElementById('connection-settings-form').addEventListener('submit', saveConnectionSettings);
  document.getElementById('reset-settings').addEventListener('click', resetSettings);
  document.getElementById('export-settings').addEventListener('click', exportSettings);
  document.getElementById('import-settings').addEventListener('click', importSettings);

  // Restaurant management
  document.getElementById('create-group').addEventListener('click', createRestaurantGroup);
  document.getElementById('test-all-connections').addEventListener('click', testAllConnections);
  document.getElementById('deploy-to-selected').addEventListener('click', deployToSelected);
  document.getElementById('export-restaurant-config').addEventListener('click', exportRestaurantConfig);

  // Deployment
  document.getElementById('deploy-script').addEventListener('click', deployScript);
  document.getElementById('check-deployment-status').addEventListener('click', checkDeploymentStatus);
  document.getElementById('update-existing-deployments').addEventListener('click', updateExistingDeployments);
  document.getElementById('remove-deployments').addEventListener('click', removeDeployments);

  // Menu event listeners
  ipcRenderer.on('menu-export-results', exportResults);

  // Progress event listeners
  ipcRenderer.on('query-progress', handleQueryProgress);
  ipcRenderer.on('connection-test-progress', handleConnectionTestProgress);
  ipcRenderer.on('deployment-progress', handleDeploymentProgress);
}

/**
 * Set up tabs
 */
function setupTabs() {
  console.log('Setting up tabs...');

  const tabLinks = document.querySelectorAll('[data-bs-toggle="tab"]');
  const tabContents = document.querySelectorAll('.tab-pane');

  console.log('Found tab links:', tabLinks.length);
  console.log('Found tab contents:', tabContents.length);

  // Ensure the first tab is active
  const firstTab = document.querySelector('#tab-query');
  const firstTabContent = document.querySelector('#query-tab');

  if (firstTab && firstTabContent) {
    // Remove active from all first
    tabLinks.forEach(tab => {
      tab.classList.remove('active');
      tab.setAttribute('aria-selected', 'false');
    });
    tabContents.forEach(content => {
      content.classList.remove('active', 'show');
    });

    // Set first tab as active
    firstTab.classList.add('active');
    firstTab.setAttribute('aria-selected', 'true');
    firstTabContent.classList.add('active', 'show');
    console.log('First tab activated');
  }

  tabLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      console.log('Tab clicked:', link.id);

      // Remove active class from all tabs
      tabLinks.forEach(tab => {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
      });
      tabContents.forEach(content => {
        content.classList.remove('active', 'show');
      });

      // Add active class to clicked tab
      link.classList.add('active');
      link.setAttribute('aria-selected', 'true');

      // Show corresponding content
      const targetId = link.getAttribute('data-bs-target');
      const targetContent = document.querySelector(targetId);
      if (targetContent) {
        targetContent.classList.add('active', 'show');
        console.log('Tab content shown:', targetId);
      } else {
        console.error('Target content not found:', targetId);
      }
    });
  });

  console.log('Tabs setup complete');
}

/**
 * Filter restaurants based on search input
 */
function filterRestaurants() {
  const searchTerm = document.getElementById('restaurant-search').value.toLowerCase();

  if (!searchTerm) {
    displayRestaurants(allRestaurants);
    return;
  }

  const filteredRestaurants = allRestaurants.filter(restaurant => {
    return restaurant.name.toLowerCase().includes(searchTerm) ||
           restaurant.id.toString().includes(searchTerm) ||
           restaurant.ip.includes(searchTerm);
  });

  displayRestaurants(filteredRestaurants);
}

/**
 * Toggle restaurant selection
 */
function toggleRestaurantSelection(e) {
  const item = e.currentTarget;
  const restaurantId = item.dataset.id;
  const restaurantIp = item.dataset.ip;

  if (item.classList.contains('selected')) {
    item.classList.remove('selected');
    selectedRestaurants = selectedRestaurants.filter(r => r.id !== restaurantId);
  } else {
    item.classList.add('selected');
    const restaurantName = item.querySelector('.restaurant-name').textContent;
    selectedRestaurants.push({
      id: restaurantId,
      name: restaurantName,
      ip: restaurantIp
    });
  }

  updateSelectedCount();
}

/**
 * Update selected restaurants count
 */
function updateSelectedCount() {
  const countElement = document.getElementById('selected-count');
  if (countElement) {
    countElement.textContent = selectedRestaurants.length;
  }
}

/**
 * Select all restaurants
 */
function selectAllRestaurants() {
  selectedRestaurants = [...allRestaurants];
  document.querySelectorAll('.restaurant-item').forEach(item => {
    item.classList.add('selected');
  });
  updateSelectedCount();
}

/**
 * Deselect all restaurants
 */
function deselectAllRestaurants() {
  selectedRestaurants = [];
  document.querySelectorAll('.restaurant-item').forEach(item => {
    item.classList.remove('selected');
  });
  updateSelectedCount();
}

/**
 * Execute query on selected restaurants
 */
async function executeQuery(e) {
  e.preventDefault();

  if (isExecutingQuery) {
    showToast('A query is already running', 'warning');
    return;
  }

  if (selectedRestaurants.length === 0) {
    showToast('Please select at least one restaurant', 'warning');
    return;
  }

  // Check if script is deployed to selected restaurants
  const undeployedRestaurants = selectedRestaurants.filter(restaurant =>
    !deploymentStatus[restaurant.id] || deploymentStatus[restaurant.id].status !== 'deployed'
  );

  if (undeployedRestaurants.length > 0) {
    const deployNow = confirm(
      `${undeployedRestaurants.length} selected restaurants don't have the script deployed.\n\n` +
      `Would you like to deploy the script first?\n\n` +
      `Click OK to deploy and then execute, or Cancel to execute anyway.`
    );

    if (deployNow) {
      // Deploy to undeployed restaurants first
      const originalSelection = [...selectedRestaurants];
      selectedRestaurants = undeployedRestaurants;

      try {
        await deployScript();
        selectedRestaurants = originalSelection;

        // Wait a moment for deployment to complete
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        selectedRestaurants = originalSelection;
        showToast('Deployment failed. Query execution cancelled.', 'danger');
        return;
      }
    }
  }

  // Get query parameters
  const startDate = document.getElementById('start-date').value;
  const endDate = document.getElementById('end-date').value;
  const customQuery = document.getElementById('custom-query').value.trim();

  // Validate date range if both are provided
  if (startDate && endDate) {
    const startMoment = moment(startDate);
    const endMoment = moment(endDate);

    if (endMoment.isBefore(startMoment)) {
      showToast('End date cannot be before start date', 'danger');
      return;
    }
  }

  // Prepare progress items
  const progressList = document.getElementById('progress-list');
  progressList.innerHTML = '';

  selectedRestaurants.forEach(restaurant => {
    const progressItem = document.createElement('div');
    progressItem.className = 'progress-item status-running';
    progressItem.id = `progress-${restaurant.id}`;
    progressItem.innerHTML = `
      <div class="progress-text">
        <div class="d-flex align-items-center">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <span>${restaurant.name}</span>
        </div>
      </div>
      <div class="progress-detail">Running query...</div>
    `;
    progressList.appendChild(progressItem);
  });

  // Clear previous results
  document.getElementById('results-container').innerHTML = `
    <div class="empty-state">
      <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3">Query running...</p>
    </div>
  `;

  // Show progress tab
  document.getElementById('tab-progress').click();

  // Execute the query
  isExecutingQuery = true;
  document.getElementById('execute-button').disabled = true;

  try {
    const queryParams = {
      restaurants: selectedRestaurants,
      startDate: startDate,
      endDate: endDate,
      customQuery: customQuery
    };

    // Save query to history
    saveQueryToHistory(queryParams);

    // Determine which execution method to use
    let response;
    const hasDeployedRestaurants = selectedRestaurants.some(restaurant =>
      deploymentStatus[restaurant.id]?.status === 'deployed'
    );

    if (hasDeployedRestaurants) {
      // Use new deployment-aware execution
      const queryString = customQuery || `SELECT * FROM SaleTransactions WHERE SaleTime BETWEEN '${startDate || '2024-01-01'}' AND '${endDate || '2024-12-31'}'`;

      response = await ipcRenderer.invoke('execute-query-with-deployment', {
        restaurants: selectedRestaurants,
        query: queryString,
        useDeployedScript: true
      });
    } else {
      // Use legacy execution method
      response = await ipcRenderer.invoke('execute-query', queryParams);
    }

    // Process results
    queryResults = response.results;

    // Display results
    displayResults(queryResults);

    // Show results tab
    document.getElementById('tab-results').click();

    // Show summary
    const totalRecords = queryResults.reduce((sum, result) => sum + (result.data?.length || 0), 0);
    const successCount = queryResults.filter(r => r.success).length;
    const failedCount = queryResults.filter(r => !r.success).length;

    showToast(
      `Query completed: ${successCount} successful, ${failedCount} failed, ${totalRecords} total records`,
      failedCount > 0 ? 'warning' : 'success'
    );

    // Handle errors
    if (response.errors && response.errors.length > 0) {
      console.warn('Some restaurants had errors:', response.errors);
      showToast(`${response.errors.length} restaurants had errors. Check progress tab.`, 'warning');
    }
  } catch (error) {
    console.error('Error executing query:', error);
    showToast('Error executing query. Check console for details.', 'danger');

    document.getElementById('results-container').innerHTML = `
      <div class="error-state">
        <div class="error-title">Error executing query</div>
        <div class="error-message">${error.message || 'Unknown error'}</div>
      </div>
    `;
  }

  isExecutingQuery = false;
  document.getElementById('execute-button').disabled = false;
}

/**
 * Update progress item for a restaurant
 */
ipcRenderer.on('query-progress', (event, progressData) => {
  const { restaurant, status, error, recordCount, message } = progressData;

  // Find the progress item for this restaurant
  const restaurantItem = document.getElementById(`progress-${selectedRestaurants.find(r => r.name === restaurant)?.id}`);

  if (!restaurantItem) return;

  // Remove previous status classes
  restaurantItem.classList.remove('status-running', 'status-completed', 'status-failed', 'status-no-results');

  // Update based on status
  switch (status) {
    case 'running':
      restaurantItem.classList.add('status-running');
      restaurantItem.innerHTML = `
        <div class="progress-text">
          <div class="d-flex align-items-center">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <span>${restaurant}</span>
          </div>
        </div>
        <div class="progress-detail">${message || 'Running query...'}</div>
      `;
      break;
    case 'completed':
      restaurantItem.classList.add('status-completed');
      restaurantItem.innerHTML = `
        <div class="progress-text">
          <div class="d-flex align-items-center">
            <span>${restaurant}</span>
            <span class="badge bg-success ms-2">Completed</span>
          </div>
        </div>
        <div class="progress-detail">Records found: ${recordCount || 0}</div>
      `;
      break;
    case 'failed':
      restaurantItem.classList.add('status-failed');
      restaurantItem.innerHTML = `
        <div class="progress-text">
          <div class="d-flex align-items-center">
            <span>${restaurant}</span>
            <span class="badge bg-danger ms-2">Failed</span>
          </div>
        </div>
        <div class="progress-detail">Error: ${error || 'Unknown error'}</div>
      `;
      break;
    case 'no-results':
      restaurantItem.classList.add('status-no-results');
      restaurantItem.innerHTML = `
        <div class="progress-text">
          <div class="d-flex align-items-center">
            <span>${restaurant}</span>
            <span class="badge bg-secondary ms-2">No Results</span>
          </div>
        </div>
        <div class="progress-detail">${message || 'No results found'}</div>
      `;
      break;
    case 'parse-error':
      restaurantItem.classList.add('status-failed');
      restaurantItem.innerHTML = `
        <div class="progress-text">
          <div class="d-flex align-items-center">
            <span>${restaurant}</span>
            <span class="badge bg-danger ms-2">Parse Error</span>
          </div>
        </div>
        <div class="progress-detail">Error: ${error || 'Error parsing results'}</div>
      `;
      break;
  }
});

/**
 * Display query results
 */
function displayResults(results) {
  const resultsContainer = document.getElementById('results-container');

  if (!results || results.length === 0) {
    resultsContainer.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">📊</div>
        <p>No results found</p>
      </div>
    `;
    return;
  }

  let html = '';
  let totalRecords = 0;

  // For each restaurant with results
  results.forEach(restaurantResult => {
    const { restaurant, ip, data, metadata } = restaurantResult;

    if (!data || data.length === 0) return;

    totalRecords += data.length;

    html += `
      <div class="restaurant-results">
        <div class="restaurant-results-header">
          <h5>
            ${restaurant}
            <span class="badge badge-record-count">${data.length} records</span>
          </h5>
          <div>
            <button class="btn btn-sm btn-outline-secondary view-details-btn" data-restaurant="${restaurant}">
              View Details
            </button>
          </div>
        </div>
        <div class="table-responsive">
          <table class="table table-sm table-striped table-bordered results-table">
            <thead class="table-dark">
              <tr>
                <th>#</th>
                <th>Sale Time</th>
                <th>Terminal</th>
              </tr>
            </thead>
            <tbody>
    `;

    data.forEach((item, index) => {
      html += `
        <tr>
          <td>${index + 1}</td>
          <td>${item.SaleTime || 'N/A'}</td>
          <td>${item.TenderPos || 'N/A'}</td>
        </tr>
      `;
    });

    html += `
            </tbody>
          </table>
        </div>
      </div>
    `;
  });

  if (html === '') {
    resultsContainer.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">📊</div>
        <p>No data found in results</p>
      </div>
    `;
  } else {
    // Add summary header
    html = `
      <div class="alert alert-info mb-3">
        <strong>Query Results:</strong> ${results.length} restaurants, ${totalRecords} total records
      </div>
    ` + html;

    resultsContainer.innerHTML = html;

    // Add event listeners to view details buttons
    document.querySelectorAll('.view-details-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        const restaurantName = btn.dataset.restaurant;
        const result = results.find(r => r.restaurant === restaurantName);
        if (result) {
          showRestaurantDetails(result);
        }
      });
    });
  }
}

/**
 * Show restaurant details modal
 */
function showRestaurantDetails(result) {
  // Create modal if it doesn't exist
  let modal = document.getElementById('restaurant-details-modal');
  if (!modal) {
    const modalHTML = `
      <div class="modal fade" id="restaurant-details-modal" tabindex="-1" aria-labelledby="restaurant-details-label" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="restaurant-details-label">Restaurant Details</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="restaurant-details-content">
              Loading...
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    modal = document.getElementById('restaurant-details-modal');
  }

  // Update modal content
  const modalContent = document.getElementById('restaurant-details-content');
  const { restaurant, ip, data, metadata } = result;

  let content = `
    <div class="mb-3">
      <h5>${restaurant}</h5>
      <p><strong>IP:</strong> ${ip}</p>
    </div>

    <div class="mb-3">
      <h6>Query Details</h6>
      <ul class="list-group">
        <li class="list-group-item"><strong>Query Time:</strong> ${metadata?.queryTime ? moment(metadata.queryTime).format('YYYY-MM-DD HH:mm:ss') : 'N/A'}</li>
        <li class="list-group-item"><strong>Start Date:</strong> ${metadata?.startDate || 'Not specified'}</li>
        <li class="list-group-item"><strong>End Date:</strong> ${metadata?.endDate || 'Not specified'}</li>
        <li class="list-group-item"><strong>Custom Query:</strong> ${metadata?.customQuery || 'Default query'}</li>
        <li class="list-group-item"><strong>Records:</strong> ${data.length}</li>
      </ul>
    </div>

    <div class="mb-3">
      <h6>Data</h6>
      <pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
    </div>
  `;

  modalContent.innerHTML = content;

  // Show modal
  const modalInstance = new bootstrap.Modal(modal);
  modalInstance.show();
}

/**
 * Export results to a JSON file
 */
async function exportResults() {
  if (!queryResults || queryResults.length === 0) {
    showToast('No results to export', 'warning');
    return;
  }

  const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
  const defaultPath = `query_results_${timestamp}.json`;

  const saveResult = await ipcRenderer.invoke('show-save-dialog', {
    defaultPath,
    filters: [
      { name: 'JSON Files', extensions: ['json'] }
    ]
  });

  if (saveResult.canceled) return;

  try {
    await ipcRenderer.invoke('write-file', {
      filePath: saveResult.filePath,
      data: JSON.stringify(queryResults, null, 2)
    });

    showToast(`Results exported to: ${saveResult.filePath}`, 'success');
  } catch (error) {
    console.error('Error exporting results:', error);
    showToast(`Error exporting results: ${error.message}`, 'danger');
  }
}

/**
 * Export results to a CSV file
 */
async function exportResultsCSV() {
  if (!queryResults || queryResults.length === 0) {
    showToast('No results to export', 'warning');
    return;
  }

  const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
  const defaultPath = `query_results_${timestamp}.csv`;

  const saveResult = await ipcRenderer.invoke('show-save-dialog', {
    defaultPath,
    filters: [
      { name: 'CSV Files', extensions: ['csv'] }
    ]
  });

  if (saveResult.canceled) return;

  try {
    // Create CSV content
    let csvContent = 'Restaurant,IP,SaleTime,TenderPos\n';

    queryResults.forEach(result => {
      const { restaurant, ip, data } = result;

      data.forEach(item => {
        const saleTime = item.SaleTime || 'N/A';
        const tenderPos = item.TenderPos || 'N/A';

        csvContent += `"${restaurant}","${ip}","${saleTime}","${tenderPos}"\n`;
      });
    });

    await ipcRenderer.invoke('write-file', {
      filePath: saveResult.filePath,
      data: csvContent
    });

    showToast(`Results exported to: ${saveResult.filePath}`, 'success');
  } catch (error) {
    console.error('Error exporting results:', error);
    showToast(`Error exporting results: ${error.message}`, 'danger');
  }
}

/**
 * Save query to history
 */
function saveQueryToHistory(queryParams) {
  const { restaurants, startDate, endDate, customQuery } = queryParams;

  const query = {
    restaurants: restaurants.map(r => ({ id: r.id, name: r.name })),
    startDate,
    endDate,
    customQuery,
    timestamp: new Date().toISOString()
  };

  ipcRenderer.invoke('save-query', query);
}

/**
 * Load query history
 */
function loadQueryHistory() {
  const historyContainer = document.getElementById('query-history-list');
  const queries = config.lastQueries || [];

  if (queries.length === 0) {
    historyContainer.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">📜</div>
        <p>No query history</p>
      </div>
    `;
    return;
  }

  historyContainer.innerHTML = '';

  queries.forEach(query => {
    const item = document.createElement('div');
    item.className = 'query-history';

    const restaurantNames = query.restaurants.map(r => r.name).join(', ');
    const restaurantCount = query.restaurants.length;
    const timestamp = moment(query.timestamp).format('YYYY-MM-DD HH:mm:ss');

    item.innerHTML = `
      <div class="query-history-timestamp">${timestamp}</div>
      <div class="query-history-details">
        <strong>${restaurantCount} restaurants</strong>
        <div><small>${restaurantNames}</small></div>
        <div>
          ${query.startDate ? `<span class="badge bg-secondary">From: ${query.startDate}</span>` : ''}
          ${query.endDate ? `<span class="badge bg-secondary">To: ${query.endDate}</span>` : ''}
          ${query.customQuery ? `<span class="badge bg-info">Custom query</span>` : ''}
        </div>
      </div>
    `;

    // Add click event to load this query
    item.addEventListener('click', () => {
      loadQueryFromHistory(query);
    });

    historyContainer.appendChild(item);
  });
}

/**
 * Load query from history
 */
function loadQueryFromHistory(query) {
  // Set date range
  if (query.startDate) {
    document.getElementById('start-date').value = query.startDate;
  }

  if (query.endDate) {
    document.getElementById('end-date').value = query.endDate;
  }

  // Set custom query
  if (query.customQuery) {
    document.getElementById('custom-query').value = query.customQuery;
  }

  // Select restaurants
  deselectAllRestaurants();

  query.restaurants.forEach(historyRestaurant => {
    const restaurantItems = document.querySelectorAll('.restaurant-item');

    for (const item of restaurantItems) {
      if (item.dataset.id === historyRestaurant.id) {
        item.click(); // Trigger the click event to select
        break;
      }
    }
  });

  // Switch to query tab
  document.getElementById('tab-query').click();

  showToast('Query loaded from history', 'info');
}

/**
 * Save settings
 */
async function saveSettings(e) {
  e.preventDefault();

  const newConfig = {
    defaultResultsLimit: parseInt(document.getElementById('results-limit').value) || 20,
    resultFileLocation: document.getElementById('results-location').value || 'Results'
  };

  try {
    await ipcRenderer.invoke('set-config', newConfig);
    config = { ...config, ...newConfig };
    showToast('Settings saved', 'success');
  } catch (error) {
    console.error('Error saving settings:', error);
    showToast('Error saving settings', 'danger');
  }
}

/**
 * Browse for results location
 */
async function browseResultsLocation() {
  const result = await ipcRenderer.invoke('select-directory');

  if (result.canceled) return;

  document.getElementById('results-location').value = result.path;
}

/**
 * Show error state
 */
function showErrorState(message) {
  const restaurantList = document.getElementById('restaurant-list');
  restaurantList.innerHTML = `
    <div class="error-state">
      <div class="error-title">Error</div>
      <div class="error-message">${message}</div>
    </div>
  `;
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
  // Create toast container if it doesn't exist
  let toastContainer = document.querySelector('.toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container';
    document.body.appendChild(toastContainer);
  }

  // Create toast
  const toastId = `toast-${Date.now()}`;
  const toast = document.createElement('div');
  toast.className = `toast align-items-center text-white bg-${type} border-0`;
  toast.setAttribute('role', 'alert');
  toast.setAttribute('aria-live', 'assertive');
  toast.setAttribute('aria-atomic', 'true');
  toast.setAttribute('id', toastId);

  toast.innerHTML = `
    <div class="d-flex">
      <div class="toast-body">
        ${message}
      </div>
      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
  `;

  toastContainer.appendChild(toast);

  // Show toast
  const bsToast = new bootstrap.Toast(toast, {
    autohide: true,
    delay: 5000
  });

  bsToast.show();

  // Remove toast after it's hidden
  toast.addEventListener('hidden.bs.toast', () => {
    toast.remove();
  });
}

// ============================================================================
// RESTAURANT MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Load restaurant groups from storage
 */
async function loadRestaurantGroups() {
  try {
    const groups = await ipcRenderer.invoke('get-restaurant-groups');
    restaurantGroups = groups || [];
    displayRestaurantGroups();
  } catch (error) {
    console.error('Error loading restaurant groups:', error);
    showToast('Error loading restaurant groups', 'danger');
  }
}

/**
 * Display restaurant groups
 */
function displayRestaurantGroups() {
  const groupsContainer = document.getElementById('restaurant-groups');

  if (restaurantGroups.length === 0) {
    groupsContainer.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">📁</div>
        <p>No groups created yet</p>
      </div>
    `;
    return;
  }

  groupsContainer.innerHTML = '';

  restaurantGroups.forEach(group => {
    const groupItem = document.createElement('div');
    groupItem.className = 'group-item';
    groupItem.dataset.groupId = group.id;

    groupItem.innerHTML = `
      <div class="group-name">${group.name}</div>
      <div class="group-count">${group.restaurants.length} restaurants</div>
    `;

    groupItem.addEventListener('click', () => selectGroup(group));
    groupsContainer.appendChild(groupItem);
  });
}

/**
 * Create a new restaurant group
 */
async function createRestaurantGroup() {
  const groupName = document.getElementById('new-group-name').value.trim();

  if (!groupName) {
    showToast('Please enter a group name', 'warning');
    return;
  }

  // Check if group name already exists
  if (restaurantGroups.find(g => g.name.toLowerCase() === groupName.toLowerCase())) {
    showToast('Group name already exists', 'warning');
    return;
  }

  const newGroup = {
    id: Date.now().toString(),
    name: groupName,
    restaurants: [],
    created: new Date().toISOString()
  };

  try {
    await ipcRenderer.invoke('save-restaurant-group', newGroup);
    restaurantGroups.push(newGroup);
    displayRestaurantGroups();
    document.getElementById('new-group-name').value = '';
    showToast(`Group "${groupName}" created successfully`, 'success');
  } catch (error) {
    console.error('Error creating group:', error);
    showToast('Error creating group', 'danger');
  }
}

/**
 * Select a restaurant group
 */
function selectGroup(group) {
  selectedGroup = group;

  // Update UI
  document.querySelectorAll('.group-item').forEach(item => {
    item.classList.remove('selected');
  });

  document.querySelector(`[data-group-id="${group.id}"]`).classList.add('selected');

  displayGroupDetails(group);
}

/**
 * Display group details
 */
function displayGroupDetails(group) {
  const detailsContainer = document.getElementById('group-details');

  let html = `
    <div class="mb-3">
      <h6>${group.name}</h6>
      <small class="text-muted">Created: ${moment(group.created).format('YYYY-MM-DD HH:mm')}</small>
    </div>

    <div class="mb-3">
      <button class="btn btn-sm btn-outline-primary me-2" onclick="addSelectedToGroup('${group.id}')">
        <i class="bi bi-plus"></i> Add Selected Restaurants
      </button>
      <button class="btn btn-sm btn-outline-success me-2" onclick="selectGroupRestaurants('${group.id}')">
        <i class="bi bi-check-all"></i> Select All in Group
      </button>
      <button class="btn btn-sm btn-outline-danger" onclick="deleteGroup('${group.id}')">
        <i class="bi bi-trash"></i> Delete Group
      </button>
    </div>

    <div class="group-restaurants">
      <h6>Restaurants (${group.restaurants.length})</h6>
  `;

  if (group.restaurants.length === 0) {
    html += '<p class="text-muted">No restaurants in this group</p>';
  } else {
    group.restaurants.forEach(restaurant => {
      html += `
        <div class="group-restaurant-item">
          <span>${restaurant.name} (${restaurant.id})</span>
          <span class="remove-btn" onclick="removeFromGroup('${group.id}', '${restaurant.id}')" title="Remove from group">
            <i class="bi bi-x"></i>
          </span>
        </div>
      `;
    });
  }

  html += '</div>';
  detailsContainer.innerHTML = html;
}

/**
 * Add selected restaurants to group
 */
async function addSelectedToGroup(groupId) {
  if (selectedRestaurants.length === 0) {
    showToast('Please select restaurants first', 'warning');
    return;
  }

  const group = restaurantGroups.find(g => g.id === groupId);
  if (!group) return;

  let addedCount = 0;

  selectedRestaurants.forEach(restaurant => {
    if (!group.restaurants.find(r => r.id === restaurant.id)) {
      group.restaurants.push({
        id: restaurant.id,
        name: restaurant.name,
        ip: restaurant.ip
      });
      addedCount++;
    }
  });

  if (addedCount > 0) {
    try {
      await ipcRenderer.invoke('save-restaurant-group', group);
      displayRestaurantGroups();
      displayGroupDetails(group);
      showToast(`Added ${addedCount} restaurants to group`, 'success');
    } catch (error) {
      console.error('Error updating group:', error);
      showToast('Error updating group', 'danger');
    }
  } else {
    showToast('All selected restaurants are already in this group', 'info');
  }
}

/**
 * Select all restaurants in a group
 */
function selectGroupRestaurants(groupId) {
  const group = restaurantGroups.find(g => g.id === groupId);
  if (!group) return;

  // Clear current selection
  deselectAllRestaurants();

  // Select restaurants in the group
  group.restaurants.forEach(groupRestaurant => {
    const restaurantItem = document.querySelector(`[data-id="${groupRestaurant.id}"]`);
    if (restaurantItem) {
      restaurantItem.click();
    }
  });

  showToast(`Selected ${group.restaurants.length} restaurants from group "${group.name}"`, 'info');
}

/**
 * Remove restaurant from group
 */
async function removeFromGroup(groupId, restaurantId) {
  const group = restaurantGroups.find(g => g.id === groupId);
  if (!group) return;

  group.restaurants = group.restaurants.filter(r => r.id !== restaurantId);

  try {
    await ipcRenderer.invoke('save-restaurant-group', group);
    displayRestaurantGroups();
    displayGroupDetails(group);
    showToast('Restaurant removed from group', 'success');
  } catch (error) {
    console.error('Error updating group:', error);
    showToast('Error updating group', 'danger');
  }
}

/**
 * Delete a restaurant group
 */
async function deleteGroup(groupId) {
  const group = restaurantGroups.find(g => g.id === groupId);
  if (!group) return;

  if (!confirm(`Are you sure you want to delete the group "${group.name}"?`)) {
    return;
  }

  try {
    await ipcRenderer.invoke('delete-restaurant-group', groupId);
    restaurantGroups = restaurantGroups.filter(g => g.id !== groupId);
    displayRestaurantGroups();

    // Clear group details
    document.getElementById('group-details').innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">📋</div>
        <p>Select a group to view details</p>
      </div>
    `;

    selectedGroup = null;
    showToast(`Group "${group.name}" deleted`, 'success');
  } catch (error) {
    console.error('Error deleting group:', error);
    showToast('Error deleting group', 'danger');
  }
}

// ============================================================================
// CONNECTION TESTING FUNCTIONS
// ============================================================================

/**
 * Test connections to all restaurants
 */
async function testAllConnections() {
  if (allRestaurants.length === 0) {
    showToast('No restaurants to test', 'warning');
    return;
  }

  showToast('Testing connections to all restaurants...', 'info');

  // Reset connection status
  connectionStatus = {};

  // Update UI to show testing state
  allRestaurants.forEach(restaurant => {
    connectionStatus[restaurant.id] = 'testing';
  });

  updateRestaurantConnectionStatus();

  try {
    const results = await ipcRenderer.invoke('test-connections', allRestaurants);

    // Update connection status with results
    results.forEach(result => {
      connectionStatus[result.restaurantId] = result.status;
    });

    updateRestaurantConnectionStatus();

    const onlineCount = results.filter(r => r.status === 'online').length;
    const offlineCount = results.filter(r => r.status === 'offline').length;

    showToast(`Connection test completed: ${onlineCount} online, ${offlineCount} offline`, 'info');
  } catch (error) {
    console.error('Error testing connections:', error);
    showToast('Error testing connections', 'danger');
  }
}

/**
 * Update restaurant items with connection status
 */
function updateRestaurantConnectionStatus() {
  document.querySelectorAll('.restaurant-item').forEach(item => {
    const restaurantId = item.dataset.id;
    const status = connectionStatus[restaurantId] || 'unknown';

    // Remove existing status indicators
    const existingStatus = item.querySelector('.connection-status');
    if (existingStatus) {
      existingStatus.remove();
    }

    // Add new status indicator
    const statusIndicator = document.createElement('span');
    statusIndicator.className = `connection-status ${status}`;
    statusIndicator.title = `Connection: ${status}`;

    const restaurantInfo = item.querySelector('div:first-child');
    restaurantInfo.insertBefore(statusIndicator, restaurantInfo.firstChild);
  });
}

/**
 * Deploy to selected restaurants
 */
async function deployToSelected() {
  if (selectedRestaurants.length === 0) {
    showToast('Please select restaurants first', 'warning');
    return;
  }

  if (!confirm(`Deploy script to ${selectedRestaurants.length} selected restaurants?`)) {
    return;
  }

  // Switch to deployment tab
  document.getElementById('tab-deployment').click();

  try {
    const deploymentConfig = {
      targetPath: document.getElementById('deployment-path').value,
      username: document.getElementById('deployment-username').value,
      password: document.getElementById('deployment-password').value,
      createFolder: document.getElementById('create-ultimate-folder').checked,
      backup: document.getElementById('backup-existing').checked,
      useSimpleDeployment: document.getElementById('simple-deployment').checked
    };

    showToast(`Starting deployment to ${selectedRestaurants.length} restaurants...`, 'info');

    const results = await ipcRenderer.invoke('deploy-script', {
      restaurants: selectedRestaurants,
      config: deploymentConfig
    });

    // Update deployment status
    results.forEach(result => {
      deploymentStatus[result.restaurantId] = {
        status: result.success ? 'deployed' : 'failed',
        timestamp: new Date().toISOString(),
        error: result.error || null
      };
    });

    updateDeploymentStatus();

    const successCount = results.filter(r => r.success).length;
    const failedCount = results.filter(r => !r.success).length;

    showToast(`Deployment completed: ${successCount} successful, ${failedCount} failed`,
              failedCount > 0 ? 'warning' : 'success');
  } catch (error) {
    console.error('Error during deployment:', error);
    showToast('Error during deployment', 'danger');
  }
}

/**
 * Export restaurant configuration
 */
async function exportRestaurantConfig() {
  const config = {
    restaurants: allRestaurants,
    groups: restaurantGroups,
    connectionStatus: connectionStatus,
    deploymentStatus: deploymentStatus,
    exportDate: new Date().toISOString()
  };

  const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
  const defaultPath = `restaurant_config_${timestamp}.json`;

  const saveResult = await ipcRenderer.invoke('show-save-dialog', {
    defaultPath,
    filters: [
      { name: 'JSON Files', extensions: ['json'] }
    ]
  });

  if (saveResult.canceled) return;

  try {
    await ipcRenderer.invoke('write-file', {
      filePath: saveResult.filePath,
      data: JSON.stringify(config, null, 2)
    });

    showToast(`Configuration exported to: ${saveResult.filePath}`, 'success');
  } catch (error) {
    console.error('Error exporting configuration:', error);
    showToast(`Error exporting configuration: ${error.message}`, 'danger');
  }
}

// ============================================================================
// DEPLOYMENT FUNCTIONS
// ============================================================================

/**
 * Load deployment status
 */
async function loadDeploymentStatus() {
  try {
    const status = await ipcRenderer.invoke('get-deployment-status');
    deploymentStatus = status || {};
    updateDeploymentStatus();
  } catch (error) {
    console.error('Error loading deployment status:', error);
    showToast('Error loading deployment status', 'danger');
  }
}

/**
 * Deploy script to selected restaurants
 */
async function deployScript() {
  if (selectedRestaurants.length === 0) {
    showToast('Please select restaurants first', 'warning');
    return;
  }

  const deploymentConfig = {
    targetPath: document.getElementById('deployment-path').value,
    username: document.getElementById('deployment-username').value,
    password: document.getElementById('deployment-password').value,
    createFolder: document.getElementById('create-ultimate-folder').checked,
    backup: document.getElementById('backup-existing').checked,
    useSimpleDeployment: document.getElementById('simple-deployment').checked
  };

  if (!deploymentConfig.username || !deploymentConfig.password) {
    showToast('Please provide username and password', 'warning');
    return;
  }

  if (!confirm(`Deploy script to ${selectedRestaurants.length} selected restaurants?`)) {
    return;
  }

  showToast(`Starting deployment to ${selectedRestaurants.length} restaurants...`, 'info');

  // Update deployment status to pending
  selectedRestaurants.forEach(restaurant => {
    deploymentStatus[restaurant.id] = {
      status: 'pending',
      timestamp: new Date().toISOString(),
      error: null
    };
  });

  updateDeploymentStatus();

  try {
    const results = await ipcRenderer.invoke('deploy-script', {
      restaurants: selectedRestaurants,
      config: deploymentConfig
    });

    // Update deployment status with results
    results.forEach(result => {
      deploymentStatus[result.restaurantId] = {
        status: result.success ? 'deployed' : 'failed',
        timestamp: new Date().toISOString(),
        error: result.error || null,
        details: result.details || null
      };
    });

    updateDeploymentStatus();

    const successCount = results.filter(r => r.success).length;
    const failedCount = results.filter(r => !r.success).length;

    showToast(`Deployment completed: ${successCount} successful, ${failedCount} failed`,
              failedCount > 0 ? 'warning' : 'success');

    // Save deployment status
    await ipcRenderer.invoke('save-deployment-status', deploymentStatus);
  } catch (error) {
    console.error('Error during deployment:', error);
    showToast('Error during deployment', 'danger');

    // Update failed restaurants
    selectedRestaurants.forEach(restaurant => {
      if (deploymentStatus[restaurant.id]?.status === 'pending') {
        deploymentStatus[restaurant.id] = {
          status: 'failed',
          timestamp: new Date().toISOString(),
          error: error.message
        };
      }
    });

    updateDeploymentStatus();
  }
}

/**
 * Check deployment status
 */
async function checkDeploymentStatus() {
  if (selectedRestaurants.length === 0) {
    showToast('Please select restaurants first', 'warning');
    return;
  }

  showToast('Checking deployment status...', 'info');

  try {
    const results = await ipcRenderer.invoke('check-deployment-status', selectedRestaurants);

    // Update deployment status with results
    results.forEach(result => {
      deploymentStatus[result.restaurantId] = {
        status: result.deployed ? 'deployed' : 'unknown',
        timestamp: new Date().toISOString(),
        error: result.error || null,
        version: result.version || null,
        lastModified: result.lastModified || null
      };
    });

    updateDeploymentStatus();

    const deployedCount = results.filter(r => r.deployed).length;
    const notDeployedCount = results.filter(r => !r.deployed).length;

    showToast(`Status check completed: ${deployedCount} deployed, ${notDeployedCount} not deployed`, 'info');

    // Save deployment status
    await ipcRenderer.invoke('save-deployment-status', deploymentStatus);
  } catch (error) {
    console.error('Error checking deployment status:', error);
    showToast('Error checking deployment status', 'danger');
  }
}

/**
 * Update existing deployments
 */
async function updateExistingDeployments() {
  const deployedRestaurants = allRestaurants.filter(restaurant =>
    deploymentStatus[restaurant.id]?.status === 'deployed'
  );

  if (deployedRestaurants.length === 0) {
    showToast('No deployed restaurants found', 'warning');
    return;
  }

  if (!confirm(`Update script on ${deployedRestaurants.length} deployed restaurants?`)) {
    return;
  }

  // Temporarily set selected restaurants to deployed ones
  const originalSelection = [...selectedRestaurants];
  selectedRestaurants = deployedRestaurants;

  // Deploy script
  await deployScript();

  // Restore original selection
  selectedRestaurants = originalSelection;
}

/**
 * Remove deployments
 */
async function removeDeployments() {
  if (selectedRestaurants.length === 0) {
    showToast('Please select restaurants first', 'warning');
    return;
  }

  if (!confirm(`Remove deployments from ${selectedRestaurants.length} selected restaurants?`)) {
    return;
  }

  showToast(`Removing deployments from ${selectedRestaurants.length} restaurants...`, 'info');

  try {
    const results = await ipcRenderer.invoke('remove-deployments', selectedRestaurants);

    // Update deployment status
    results.forEach(result => {
      if (result.success) {
        delete deploymentStatus[result.restaurantId];
      } else {
        deploymentStatus[result.restaurantId] = {
          status: 'failed',
          timestamp: new Date().toISOString(),
          error: result.error || 'Failed to remove deployment'
        };
      }
    });

    updateDeploymentStatus();

    const successCount = results.filter(r => r.success).length;
    const failedCount = results.filter(r => !r.success).length;

    showToast(`Removal completed: ${successCount} successful, ${failedCount} failed`,
              failedCount > 0 ? 'warning' : 'success');

    // Save deployment status
    await ipcRenderer.invoke('save-deployment-status', deploymentStatus);
  } catch (error) {
    console.error('Error removing deployments:', error);
    showToast('Error removing deployments', 'danger');
  }
}

/**
 * Update deployment status display
 */
function updateDeploymentStatus() {
  const statusContainer = document.getElementById('deployment-status-list');

  // Update statistics
  const deployedCount = Object.values(deploymentStatus).filter(s => s.status === 'deployed').length;
  const pendingCount = Object.values(deploymentStatus).filter(s => s.status === 'pending').length;
  const failedCount = Object.values(deploymentStatus).filter(s => s.status === 'failed').length;

  document.getElementById('deployed-count').textContent = deployedCount;
  document.getElementById('pending-count').textContent = pendingCount;
  document.getElementById('failed-count').textContent = failedCount;

  // Update status list
  if (Object.keys(deploymentStatus).length === 0) {
    statusContainer.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">📦</div>
        <p>No deployments yet</p>
      </div>
    `;
    return;
  }

  statusContainer.innerHTML = '';

  Object.entries(deploymentStatus).forEach(([restaurantId, status]) => {
    const restaurant = allRestaurants.find(r => r.id === restaurantId);
    if (!restaurant) return;

    const statusItem = document.createElement('div');
    statusItem.className = `deployment-item deployment-status-${status.status}`;

    statusItem.innerHTML = `
      <div class="deployment-header">
        <div class="deployment-name">${restaurant.name}</div>
        <div class="deployment-status">
          <span class="badge bg-${getStatusColor(status.status)}">${status.status.toUpperCase()}</span>
        </div>
      </div>
      <div class="deployment-details">
        <div>IP: ${restaurant.ip}</div>
        <div>Last Updated: ${moment(status.timestamp).format('YYYY-MM-DD HH:mm:ss')}</div>
        ${status.error ? `<div class="text-danger">Error: ${status.error}</div>` : ''}
        ${status.version ? `<div>Version: ${status.version}</div>` : ''}
      </div>
    `;

    statusContainer.appendChild(statusItem);
  });
}

/**
 * Get status color for badges
 */
function getStatusColor(status) {
  switch (status) {
    case 'deployed': return 'success';
    case 'pending': return 'warning';
    case 'failed': return 'danger';
    default: return 'secondary';
  }
}

// ============================================================================
// ENHANCED SETTINGS FUNCTIONS
// ============================================================================

/**
 * Save connection settings
 */
async function saveConnectionSettings(e) {
  e.preventDefault();

  const connectionConfig = {
    defaultUsername: document.getElementById('default-username').value,
    defaultPassword: document.getElementById('default-password').value,
    connectionTimeout: parseInt(document.getElementById('connection-timeout').value) || 15,
    autoRetry: document.getElementById('auto-retry').checked
  };

  try {
    await ipcRenderer.invoke('set-config', connectionConfig);
    config = { ...config, ...connectionConfig };
    showToast('Connection settings saved', 'success');
  } catch (error) {
    console.error('Error saving connection settings:', error);
    showToast('Error saving connection settings', 'danger');
  }
}

/**
 * Reset settings to defaults
 */
async function resetSettings() {
  if (!confirm('Are you sure you want to reset all settings to defaults?')) {
    return;
  }

  try {
    await ipcRenderer.invoke('reset-config');

    // Reload configuration
    await loadConfig();

    // Reset form values
    document.getElementById('results-limit').value = 20;
    document.getElementById('results-location').value = 'Results';
    document.getElementById('query-timeout').value = 30;
    document.getElementById('max-concurrent').value = 3;
    document.getElementById('default-username').value = 'Administrator';
    document.getElementById('default-password').value = 'Jvr963*14';
    document.getElementById('connection-timeout').value = 15;
    document.getElementById('auto-retry').checked = true;
    document.getElementById('log-level').value = 'info';
    document.getElementById('backup-retention').value = 30;
    document.getElementById('enable-notifications').checked = true;
    document.getElementById('auto-save-results').checked = true;

    showToast('Settings reset to defaults', 'success');
  } catch (error) {
    console.error('Error resetting settings:', error);
    showToast('Error resetting settings', 'danger');
  }
}

/**
 * Export settings
 */
async function exportSettings() {
  const settingsData = {
    config: config,
    restaurantGroups: restaurantGroups,
    deploymentStatus: deploymentStatus,
    connectionStatus: connectionStatus,
    exportDate: new Date().toISOString(),
    version: '1.0.0'
  };

  const timestamp = moment().format('YYYY-MM-DD_HH-mm-ss');
  const defaultPath = `settings_backup_${timestamp}.json`;

  const saveResult = await ipcRenderer.invoke('show-save-dialog', {
    defaultPath,
    filters: [
      { name: 'JSON Files', extensions: ['json'] }
    ]
  });

  if (saveResult.canceled) return;

  try {
    await ipcRenderer.invoke('write-file', {
      filePath: saveResult.filePath,
      data: JSON.stringify(settingsData, null, 2)
    });

    showToast(`Settings exported to: ${saveResult.filePath}`, 'success');
  } catch (error) {
    console.error('Error exporting settings:', error);
    showToast(`Error exporting settings: ${error.message}`, 'danger');
  }
}

/**
 * Import settings
 */
async function importSettings() {
  const openResult = await ipcRenderer.invoke('show-open-dialog', {
    filters: [
      { name: 'JSON Files', extensions: ['json'] }
    ],
    properties: ['openFile']
  });

  if (openResult.canceled) return;

  try {
    const fileContent = await ipcRenderer.invoke('read-file', openResult.filePaths[0]);
    const settingsData = JSON.parse(fileContent);

    if (!settingsData.config) {
      showToast('Invalid settings file format', 'danger');
      return;
    }

    if (!confirm('This will overwrite your current settings. Continue?')) {
      return;
    }

    // Import configuration
    if (settingsData.config) {
      await ipcRenderer.invoke('set-config', settingsData.config);
      config = settingsData.config;
    }

    // Import restaurant groups
    if (settingsData.restaurantGroups) {
      for (const group of settingsData.restaurantGroups) {
        await ipcRenderer.invoke('save-restaurant-group', group);
      }
      restaurantGroups = settingsData.restaurantGroups;
      displayRestaurantGroups();
    }

    // Import deployment status
    if (settingsData.deploymentStatus) {
      await ipcRenderer.invoke('save-deployment-status', settingsData.deploymentStatus);
      deploymentStatus = settingsData.deploymentStatus;
      updateDeploymentStatus();
    }

    // Import connection status
    if (settingsData.connectionStatus) {
      connectionStatus = settingsData.connectionStatus;
      updateRestaurantConnectionStatus();
    }

    // Reload configuration
    await loadConfig();

    showToast('Settings imported successfully', 'success');
  } catch (error) {
    console.error('Error importing settings:', error);
    showToast(`Error importing settings: ${error.message}`, 'danger');
  }
}

// ============================================================================
// PROGRESS HANDLERS
// ============================================================================

/**
 * Handle query progress updates
 */
function handleQueryProgress(event, progress) {
  const { restaurant, status, message, error } = progress;

  // Find or create progress item
  let progressItem = document.querySelector(`[data-restaurant="${restaurant}"]`);

  if (!progressItem) {
    const progressList = document.getElementById('progress-list');
    progressItem = document.createElement('div');
    progressItem.className = 'progress-item';
    progressItem.dataset.restaurant = restaurant;

    progressItem.innerHTML = `
      <div class="progress-header">
        <span class="restaurant-name">${restaurant}</span>
        <span class="progress-status badge bg-secondary">Pending</span>
      </div>
      <div class="progress-message">Waiting to start...</div>
      <div class="progress-bar-container">
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: 0%"></div>
        </div>
      </div>
    `;

    progressList.appendChild(progressItem);
  }

  // Update status
  const statusBadge = progressItem.querySelector('.progress-status');
  const messageDiv = progressItem.querySelector('.progress-message');
  const progressBar = progressItem.querySelector('.progress-bar');

  switch (status) {
    case 'running':
      statusBadge.className = 'progress-status badge bg-primary';
      statusBadge.textContent = 'Running';
      messageDiv.textContent = message || 'Executing query...';
      progressBar.style.width = '50%';
      progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
      break;

    case 'completed':
      statusBadge.className = 'progress-status badge bg-success';
      statusBadge.textContent = 'Completed';
      messageDiv.textContent = 'Query completed successfully';
      progressBar.style.width = '100%';
      progressBar.className = 'progress-bar bg-success';
      break;

    case 'failed':
      statusBadge.className = 'progress-status badge bg-danger';
      statusBadge.textContent = 'Failed';
      messageDiv.textContent = error || 'Query failed';
      progressBar.style.width = '100%';
      progressBar.className = 'progress-bar bg-danger';
      break;
  }
}

/**
 * Handle connection test progress updates
 */
function handleConnectionTestProgress(event, progress) {
  const { restaurant, status } = progress;

  // Update connection status in the UI
  connectionStatus[restaurant] = status;
  updateRestaurantConnectionStatus();

  // Show toast for individual updates if needed
  if (status === 'online') {
    console.log(`${restaurant} is online`);
  } else if (status === 'offline') {
    console.log(`${restaurant} is offline`);
  }
}

/**
 * Handle deployment progress updates
 */
function handleDeploymentProgress(event, progress) {
  const { restaurant, status, error } = progress;

  // Find restaurant in deployment status and update
  const restaurantObj = allRestaurants.find(r => r.name === restaurant);
  if (restaurantObj) {
    deploymentStatus[restaurantObj.id] = {
      status: status,
      timestamp: new Date().toISOString(),
      error: error || null
    };

    updateDeploymentStatus();
  }

  // Show progress notification
  switch (status) {
    case 'deploying':
      showToast(`Deploying to ${restaurant}...`, 'info');
      break;
    case 'deployed':
      showToast(`Successfully deployed to ${restaurant}`, 'success');
      break;
    case 'failed':
      showToast(`Failed to deploy to ${restaurant}: ${error}`, 'danger');
      break;
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Clear progress display
 */
function clearProgress() {
  const progressList = document.getElementById('progress-list');
  progressList.innerHTML = '';
}

/**
 * Deselect all restaurants
 */
function deselectAllRestaurants() {
  selectedRestaurants = [];
  document.querySelectorAll('.restaurant-item').forEach(item => {
    item.classList.remove('selected');
  });
  updateSelectedCount();
}

/**
 * Update selected restaurant count display
 */
function updateSelectedCount() {
  const countElement = document.getElementById('selected-count');
  if (countElement) {
    countElement.textContent = selectedRestaurants.length;
  }

  // Update button states
  const executeButton = document.getElementById('execute-button');
  if (executeButton) {
    executeButton.disabled = selectedRestaurants.length === 0 || isExecutingQuery;
  }
}