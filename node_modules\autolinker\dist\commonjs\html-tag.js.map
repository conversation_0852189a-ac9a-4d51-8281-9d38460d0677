{"version": 3, "file": "html-tag.js", "sourceRoot": "", "sources": ["../../src/html-tag.ts"], "names": [], "mappings": ";;;AAAA,iCAAkC;AAElC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAyEG;AACH;IAkCI;;;OAGG;IACH,iBAAY,GAAoB;QAApB,oBAAA,EAAA,QAAoB;QArChC;;;;;;;WAOG;QACK,YAAO,GAAW,EAAE,CAAC,CAAC,gGAAgG;QAE9H;;;;;WAKG;QACK,UAAK,GAA8B,EAAE,CAAC,CAAC,gGAAgG;QAE/I;;;;WAIG;QACK,cAAS,GAAW,EAAE,CAAC,CAAC,gGAAgG;QAEhI;;;;;WAKG;QACO,oBAAe,GAAG,KAAK,CAAC,CAAC,gGAAgG;QAO/H,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,mFAAmF;IAC9I,CAAC;IAED;;;;;OAKG;IACH,4BAAU,GAAV,UAAW,OAAe;QACtB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,4BAAU,GAAV;QACI,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;;;;;OAMG;IACH,yBAAO,GAAP,UAAQ,QAAgB,EAAE,SAAiB;QACvC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC/B,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACH,yBAAO,GAAP,UAAQ,QAAgB;QACpB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACH,0BAAQ,GAAR,UAAS,KAAiC;QACtC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,0BAAQ,GAAR;QACI,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACH,0BAAQ,GAAR,UAAS,QAAgB;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACH,0BAAQ,GAAR,UAAS,QAAgB;QACrB,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,EAC3B,eAAe,GAAG,IAAI,CAAC,eAAe,EACtC,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAC5D,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,EAC5C,QAA4B,CAAC;QAEjC,OAAO,CAAC,QAAQ,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE;YACpC,IAAI,IAAA,eAAO,EAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC1B;SACJ;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACH,6BAAW,GAAX,UAAY,QAAgB;QACxB,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,EAC3B,eAAe,GAAG,IAAI,CAAC,eAAe,EACtC,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,EAC5D,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,EAC/C,WAA+B,CAAC;QAEpC,OAAO,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE;YAC5D,IAAI,GAAG,GAAG,IAAA,eAAO,EAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YACxC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;gBACZ,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;aAC1B;SACJ;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACH,0BAAQ,GAAR;QACI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACH,0BAAQ,GAAR,UAAS,QAAgB;QACrB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,GAAG,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;OAKG;IACH,8BAAY,GAAZ,UAAa,IAAY;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACH,8BAAY,GAAZ,UAAa,IAAY;QACrB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACH,8BAAY,GAAZ;QACI,OAAO,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,8BAAY,GAAZ;QACI,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACH,gCAAc,GAAd;QACI,IAAI,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,EAC3B,QAAQ,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAEpC,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,mDAAmD;QAE9F,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED;;;;;;OAMG;IACO,+BAAa,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAC,CAAC,4DAA4D;QAExF,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,EACvB,QAAQ,GAAa,EAAE,CAAC;QAE5B,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;YACpB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;gBAC5B,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;aAClD;SACJ;QACD,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IACL,cAAC;AAAD,CAAC,AApQD,IAoQC;AApQY,0BAAO"}