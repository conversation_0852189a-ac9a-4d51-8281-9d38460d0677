/* Restaurant Query Tool Styles */
:root {
  --primary-color: #ffbc0d;
  --secondary-color: #db0007;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --gray-color: #6c757d;
  --border-radius: 0.25rem;
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease-in-out;
}

body {
  padding: 20px;
  background-color: var(--light-color);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header styles */
.app-header {
  margin-bottom: 1.5rem;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.app-header h1 {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-logo {
  height: 40px;
}

/* Card styles */
.card {
  margin-bottom: 20px;
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.card:hover {
  box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2);
}

.card-header {
  font-weight: 600;
  border-bottom: none;
}

.card-header.bg-primary {
  background-color: var(--primary-color) !important;
  color: #333;
}

/* Restaurant list styles */
.restaurant-container {
  max-height: 500px;
  overflow-y: auto;
  scrollbar-width: thin;
  padding: 0.5rem;
}

.restaurant-container::-webkit-scrollbar {
  width: 8px;
}

.restaurant-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.restaurant-container::-webkit-scrollbar-thumb {
  background: var(--gray-color);
  border-radius: 4px;
}

.restaurant-search {
  margin-bottom: 10px;
}

.restaurant-item {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: var(--border-radius);
  background-color: #fff;
  border: 1px solid #dee2e6;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.restaurant-item:hover {
  background-color: #f1f3f5;
}

.restaurant-item.selected {
  background-color: #fff3cd;
  border-color: var(--primary-color);
}

.restaurant-item .restaurant-name {
  font-weight: 500;
}

.restaurant-item .restaurant-id {
  color: var(--gray-color);
  font-size: 0.85rem;
}

.restaurant-item .restaurant-ip {
  color: var(--gray-color);
  font-size: 0.85rem;
}

/* Form styles */
.form-control {
  border-radius: var(--border-radius);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(255, 188, 13, 0.25);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #333;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #e5a90c;
  border-color: #e5a90c;
  color: #333;
}

.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-secondary:hover, .btn-secondary:focus {
  background-color: #c00006;
  border-color: #c00006;
}

/* Progress styles */
.progress-container {
  margin-top: 20px;
}

.progress-item {
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.progress-text {
  font-weight: 500;
}

.progress-detail {
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.status-running {
  background-color: #fff3cd;
  border-left: 4px solid var(--warning-color);
}

.status-completed {
  background-color: #d1e7dd;
  border-left: 4px solid var(--success-color);
}

.status-failed {
  background-color: #f8d7da;
  border-left: 4px solid var(--danger-color);
}

.status-no-results {
  background-color: #e2e3e5;
  border-left: 4px solid var(--gray-color);
}

/* Results styles */
.results-container {
  max-height: 500px;
  overflow-y: auto;
  scrollbar-width: thin;
}

.results-container::-webkit-scrollbar {
  width: 8px;
}

.results-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.results-container::-webkit-scrollbar-thumb {
  background: var(--gray-color);
  border-radius: 4px;
}

.restaurant-results {
  margin-bottom: 1.5rem;
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.restaurant-results-header {
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.restaurant-results-header h5 {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.results-table {
  margin-bottom: 0;
}

.results-table th {
  position: sticky;
  top: 0;
  background-color: #343a40;
  color: white;
}

.results-table tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.badge-record-count {
  background-color: var(--primary-color);
  color: #333;
  padding: 0.35rem 0.65rem;
  border-radius: 50px;
  font-size: 0.75rem;
}

/* Loading spinner */
.spinner-border {
  width: 1.2rem;
  height: 1.2rem;
  margin-right: 8px;
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: var(--gray-color);
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

/* Toast notifications */
.toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

.toast {
  min-width: 300px;
}

/* Query history */
.query-history {
  padding: 0.5rem;
  cursor: pointer;
  transition: var(--transition);
  border-bottom: 1px solid #dee2e6;
}

.query-history:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.query-history-timestamp {
  font-size: 0.75rem;
  color: var(--gray-color);
}

.query-history-details {
  font-size: 0.85rem;
}

/* Settings */
.settings-item {
  padding: 1rem 0;
  border-bottom: 1px solid #dee2e6;
}

.settings-item:last-child {
  border-bottom: none;
}

/* Error state */
.error-state {
  padding: 1rem;
  background-color: #f8d7da;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

.error-title {
  font-weight: 600;
  color: var(--danger-color);
}

.error-message {
  margin-top: 0.5rem;
}

/* Restaurant Groups */
.groups-container {
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
}

.groups-container::-webkit-scrollbar {
  width: 8px;
}

.groups-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.groups-container::-webkit-scrollbar-thumb {
  background: var(--gray-color);
  border-radius: 4px;
}

.group-item {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: var(--border-radius);
  background-color: #fff;
  border: 1px solid #dee2e6;
  transition: var(--transition);
  cursor: pointer;
}

.group-item:hover {
  background-color: #f1f3f5;
}

.group-item.selected {
  background-color: #e3f2fd;
  border-color: var(--info-color);
}

.group-item .group-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.group-item .group-count {
  color: var(--gray-color);
  font-size: 0.85rem;
}

.group-restaurants {
  margin-top: 0.5rem;
}

.group-restaurant-item {
  padding: 0.5rem;
  margin-bottom: 0.25rem;
  background-color: #f8f9fa;
  border-radius: var(--border-radius);
  font-size: 0.85rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-restaurant-item .remove-btn {
  color: var(--danger-color);
  cursor: pointer;
  font-size: 0.75rem;
}

.group-restaurant-item .remove-btn:hover {
  color: #c82333;
}

/* Deployment Status */
.deployment-item {
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
  border-left: 4px solid transparent;
}

.deployment-item .deployment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.deployment-item .deployment-name {
  font-weight: 500;
}

.deployment-item .deployment-status {
  font-size: 0.85rem;
}

.deployment-item .deployment-details {
  font-size: 0.85rem;
  color: var(--gray-color);
}

.deployment-status-deployed {
  background-color: #d1e7dd;
  border-left-color: var(--success-color);
}

.deployment-status-pending {
  background-color: #fff3cd;
  border-left-color: var(--warning-color);
}

.deployment-status-failed {
  background-color: #f8d7da;
  border-left-color: var(--danger-color);
}

.deployment-status-unknown {
  background-color: #e2e3e5;
  border-left-color: var(--gray-color);
}

/* Statistics */
.stat-item {
  padding: 1rem;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.85rem;
  color: var(--gray-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Connection Status */
.connection-status {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.connection-status.online {
  background-color: var(--success-color);
}

/* Tab Content Fix */
.tab-content {
  min-height: 400px;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block !important;
}

.tab-pane.show.active {
  display: block !important;
}

/* Ensure cards are visible */
.card {
  display: block !important;
}

.card-body {
  display: block !important;
}

.connection-status.offline {
  background-color: var(--danger-color);
}

.connection-status.unknown {
  background-color: var(--gray-color);
}

.connection-status.testing {
  background-color: var(--warning-color);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Enhanced Progress Items */
.progress-item .progress-actions {
  margin-top: 0.5rem;
}

.progress-item .progress-actions .btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Enhanced Results */
.results-summary {
  background-color: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-bottom: 1rem;
}

.results-summary .summary-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.results-summary .summary-stat {
  flex: 1;
}

.results-summary .summary-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--info-color);
}

.results-summary .summary-label {
  font-size: 0.85rem;
  color: var(--gray-color);
}

/* Enhanced Restaurant Items */
.restaurant-item .restaurant-status {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.restaurant-item .last-query {
  font-size: 0.75rem;
  color: var(--gray-color);
}

/* Tabs Enhancement */
.nav-tabs .nav-link {
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  transition: var(--transition);
}

.nav-tabs .nav-link:hover {
  background-color: rgba(255, 188, 13, 0.1);
}

.nav-tabs .nav-link.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #333;
}

/* Loading States */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.card {
  position: relative;
}

/* Responsive Design */
@media (max-width: 768px) {
  .restaurant-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .restaurant-item .restaurant-ip {
    margin-top: 0.25rem;
  }

  .results-summary .summary-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .deployment-item .deployment-header {
    flex-direction: column;
    align-items: flex-start;
  }
}