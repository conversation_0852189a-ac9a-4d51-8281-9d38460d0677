const { app, BrowserWindow, ipcMain, dialog, Menu, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const Store = require('electron-store');

// Try to enable hot reload for development
try {
  require('electron-reloader')(module, {
    ignore: ['src/renderer/js/config.js']
  });
} catch {}

// Initialize configuration store
const store = new Store({
  name: 'config',
  defaults: {
    windowSize: { width: 1200, height: 800 },
    lastQueries: [],
    maxSavedQueries: 10,
    defaultResultsLimit: 20,
    resultFileLocation: 'Results',
    queryTimeout: 30,
    maxConcurrent: 3,
    defaultUsername: 'Administrator',
    defaultPassword: 'Jvr963*14',
    connectionTimeout: 15,
    autoRetry: true,
    logLevel: 'info',
    backupRetention: 30,
    enableNotifications: true,
    autoSaveResults: true
  }
});

// Initialize additional stores
const groupsStore = new Store({
  name: 'restaurant-groups',
  defaults: {
    groups: []
  }
});

const deploymentStore = new Store({
  name: 'deployment-status',
  defaults: {
    status: {}
  }
});

// Global reference to mainWindow to prevent garbage collection
let mainWindow;

/**
 * Create the main application window
 */
function createWindow() {
  // Get saved window size or use default
  const { width, height } = store.get('windowSize');

  // Create the browser window
  mainWindow = new BrowserWindow({
    width,
    height,
    title: "McDonald's Restaurant Query Tool",
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    show: true, // Show immediately for debugging
    icon: path.join(__dirname, './assets/icon.png')
  });

  // Load the index.html file
  mainWindow.loadFile(path.join(__dirname, './index.html'));

  // Always open DevTools for debugging
  mainWindow.webContents.openDevTools();

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    console.log('Window ready to show');
    mainWindow.show();
    mainWindow.focus();
  });

  // Add error handling for loading
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('Failed to load:', errorCode, errorDescription);
  });

  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Page finished loading');
  });

  // Save window size on close
  mainWindow.on('close', () => {
    store.set('windowSize', mainWindow.getBounds());
  });

  // Create application menu
  createAppMenu();

  return mainWindow;
}

/**
 * Create the application menu
 */
function createAppMenu() {
  const isMac = process.platform === 'darwin';

  const template = [
    ...(isMac ? [{
      label: app.name,
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideothers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    }] : []),
    {
      label: 'File',
      submenu: [
        {
          label: 'Export Results',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            mainWindow.webContents.send('menu-export-results');
          }
        },
        { type: 'separator' },
        isMac ? { role: 'close' } : { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'delete' },
        { type: 'separator' },
        { role: 'selectAll' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'Documentation',
          click: async () => {
            const docPath = path.join(__dirname, './user-guide.pdf');
            if (fs.existsSync(docPath)) {
              shell.openPath(docPath);
            } else {
              dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: 'Documentation',
                message: 'Documentation file not found.',
                detail: 'Please contact support for assistance.'
              });
            }
          }
        },
        {
          label: 'About',
          click: async () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About',
              message: 'McDonald\'s Restaurant Query Tool',
              detail: 'Version 1.0.0\n\nA tool for running queries on restaurant servers.'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Initialize the app
app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    // On macOS re-create a window when the dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

/**
 * Load restaurants from file
 */
ipcMain.handle('read-restaurants', async () => {
  try {
    const filePath = path.join(app.getAppPath(), 'Restaurants.txt');
    if (!fs.existsSync(filePath)) {
      return { error: 'Restaurants.txt file not found. Please place it in the application directory.' };
    }

    const data = fs.readFileSync(filePath, 'utf8');
    const lines = data.split('\n');

    const restaurants = [];
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line || line.startsWith('EID') || line.startsWith('MA')) continue;

      // Extract EID, NAME, IP_BO
      const parts = line.split('\t').filter(part => part.trim() !== '');
      if (parts.length >= 3) {
        const id = parts[0].trim();
        const name = parts[1].trim();
        const ip = parts[2].trim();

        // Skip empty entries
        if (id && name && ip) {
          restaurants.push({
            id: id,
            name: name,
            ip: ip
          });
        }
      }
    }

    // If no restaurants were found
    if (restaurants.length === 0) {
      return { error: 'No valid restaurant entries found in Restaurants.txt' };
    }

    return { restaurants };
  } catch (error) {
    console.error('Error reading restaurants file:', error);
    return { error: `Error reading restaurants file: ${error.message}` };
  }
});

/**
 * Get configuration
 */
ipcMain.handle('get-config', () => {
  return store.store;
});

/**
 * Set configuration
 */
ipcMain.handle('set-config', (event, config) => {
  Object.keys(config).forEach(key => {
    store.set(key, config[key]);
  });
  return { success: true };
});

/**
 * Save query to history
 */
ipcMain.handle('save-query', (event, query) => {
  const maxQueries = store.get('maxSavedQueries');
  let queries = store.get('lastQueries') || [];

  // Add timestamp to query
  query.timestamp = new Date().toISOString();

  // Add to beginning of array
  queries.unshift(query);

  // Limit to max number of queries
  if (queries.length > maxQueries) {
    queries = queries.slice(0, maxQueries);
  }

  store.set('lastQueries', queries);
  return { success: true };
});

/**
 * Execute query on selected restaurant(s) using deployed script
 */
ipcMain.handle('execute-query-with-deployment', async (event, queryParams) => {
  const { restaurants, query, useDeployedScript } = queryParams;
  const results = [];

  for (const restaurant of restaurants) {
    try {
      // Send progress update
      mainWindow.webContents.send('query-progress', {
        restaurant: restaurant.name,
        status: 'running',
        message: 'Executing query using deployed script...'
      });

      const queryResult = await executeQueryOnRestaurant(restaurant, query, useDeployedScript);

      results.push({
        restaurantId: restaurant.id,
        restaurant: restaurant.name,
        ip: restaurant.ip,
        success: queryResult.success,
        data: queryResult.data || [],
        error: queryResult.error,
        metadata: {
          queryTime: new Date().toISOString(),
          query: query,
          useDeployedScript: useDeployedScript
        }
      });

      // Send progress update
      mainWindow.webContents.send('query-progress', {
        restaurant: restaurant.name,
        status: queryResult.success ? 'completed' : 'failed',
        error: queryResult.error
      });
    } catch (error) {
      console.error(`Error executing query on ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        restaurant: restaurant.name,
        ip: restaurant.ip,
        success: false,
        data: [],
        error: error.message
      });
    }
  }

  return { results, errors: results.filter(r => !r.success) };
});

/**
 * Execute query on a single restaurant using deployed script
 */
async function executeQueryOnRestaurant(restaurant, query, useDeployedScript) {
  return new Promise((resolve) => {
    console.log(`Executing query on ${restaurant.name} (${restaurant.ip})`);

    if (useDeployedScript) {
      // Use batch file approach for remote execution
      executeQueryWithBatchFile(restaurant, query, resolve);
    } else {
      // Use local script execution (legacy method)
      executeQueryLocally(restaurant, query, resolve);
    }
  });
}

/**
 * Execute query using PowerShell approach (more reliable)
 */
function executeQueryWithBatchFile(restaurant, query, resolve) {
  const remotePath = `\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools`;
  const remoteScriptPath = `${remotePath}\\option1_query.bat`;
  const remoteResultsPath = `${remotePath}\\Results`;

  // Create PowerShell script for query execution
  const psScript = path.join(app.getAppPath(), 'temp_query.ps1');

  // Create a simple query file
  const queryFile = path.join(app.getAppPath(), 'temp_query.sql');
  fs.writeFileSync(queryFile, query);

  // Get current date for file naming (same format as the script uses)
  const currentDate = new Date();
  const dateStr = currentDate.getFullYear().toString() +
                  (currentDate.getMonth() + 1).toString().padStart(2, '0') +
                  currentDate.getDate().toString().padStart(2, '0');

  // Create PowerShell script content
  let scriptContent = `$ErrorActionPreference = "Stop"

Write-Output "Executing query on ${restaurant.ip}..."

try {
    # Test network connectivity
    $remotePath = "${remotePath.replace(/\\/g, '\\\\')}"
    $remoteScriptPath = "${remoteScriptPath.replace(/\\/g, '\\\\')}"
    $remoteResultsPath = "${remoteResultsPath.replace(/\\/g, '\\\\')}"

    Write-Output "Testing network connectivity..."
    if (-not (Test-Path "\\\\${restaurant.ip}\\D$")) {
        throw "Cannot access \\\\${restaurant.ip}\\D$ - check network connectivity and permissions"
    }

    Write-Output "Network accessible, checking deployed script..."
    if (-not (Test-Path $remoteScriptPath)) {
        throw "Script not deployed to ${restaurant.ip}. Please deploy first."
    }

    Write-Output "Script found, preparing query execution..."

    # Clean up any existing result files
    if (Test-Path $remoteResultsPath) {
        Remove-Item "$remoteResultsPath\\*.txt" -Force -ErrorAction SilentlyContinue
        Remove-Item "$remoteResultsPath\\*.json" -Force -ErrorAction SilentlyContinue
        Remove-Item "$remoteResultsPath\\*-status.txt" -Force -ErrorAction SilentlyContinue
    }


    # Copy query file to remote location
    Write-Output "Copying query to remote location..."
    Copy-Item "${queryFile.replace(/\\/g, '\\\\')}" "$remotePath\\temp_query.sql" -Force

    # Read the query content
    $queryContent = Get-Content "${queryFile.replace(/\\/g, '\\\\')}" -Raw
    Write-Output "Query to execute: $queryContent"

    # Create a simple execution script
    $executeScript = @"
@echo off
cd /d D:\\Newpos61\\Support_Tools\\Ultimate_support_tools
call option1_query.bat -query "$queryContent"
echo Query execution completed
"@

    $executeScript | Out-File -FilePath "$remotePath\\run_query.bat" -Encoding ASCII

    Write-Output "Attempting remote execution..."

    # Try to execute using Invoke-Command (requires WinRM)
    try {
        $credential = New-Object System.Management.Automation.PSCredential("Administrator", (ConvertTo-SecureString "Jvr963*14" -AsPlainText -Force))

        Write-Output "Trying PowerShell remoting..."
        Invoke-Command -ComputerName ${restaurant.ip} -Credential $credential -ScriptBlock {
            Set-Location "D:\\Newpos61\\Support_Tools\\Ultimate_support_tools"
            & ".\\run_query.bat"
        } -ErrorAction Stop

        Write-Output "PowerShell remoting succeeded"

    } catch {
        Write-Output "PowerShell remoting failed: $($_.Exception.Message)"
        Write-Output "Trying alternative execution method..."

        # Alternative: Use WMI to start the process
        try {
            $processClass = [wmiclass]"\\\\${restaurant.ip}\\root\\cimv2:Win32_Process"
            $result = $processClass.Create("cmd.exe /c D:\\Newpos61\\Support_Tools\\Ultimate_support_tools\\run_query.bat", "D:\\Newpos61\\Support_Tools\\Ultimate_support_tools")

            if ($result.ReturnValue -eq 0) {
                Write-Output "WMI execution started successfully"
            } else {
                Write-Output "WMI execution failed with code: $($result.ReturnValue)"
            }
        } catch {
            Write-Output "WMI execution also failed: $($_.Exception.Message)"
        }
    }

    # Wait for execution to complete
    Write-Output "Waiting for query execution to complete..."
    Start-Sleep -Seconds 20


    # Check for result files
    Write-Output "Looking for result files..."

    if (Test-Path $remoteResultsPath) {
        $jsonFiles = Get-ChildItem "$remoteResultsPath\\*.json" -ErrorAction SilentlyContinue
        $txtFiles = Get-ChildItem "$remoteResultsPath\\*.txt" -ErrorAction SilentlyContinue

        Write-Output "=== RESULTS DIRECTORY LISTING ==="
        Get-ChildItem $remoteResultsPath -ErrorAction SilentlyContinue | ForEach-Object { Write-Output $_.Name }
        Write-Output "=== END DIRECTORY LISTING ==="

        if ($jsonFiles) {
            foreach ($file in $jsonFiles) {
                Write-Output "Found JSON file: $($file.FullName)"
                Write-Output "=== JSON CONTENT START ==="
                Get-Content $file.FullName -ErrorAction SilentlyContinue
                Write-Output "=== JSON CONTENT END ==="
                Remove-Item $file.FullName -Force -ErrorAction SilentlyContinue
            }
        } elseif ($txtFiles) {
            foreach ($file in $txtFiles) {
                Write-Output "Found text file: $($file.FullName)"
                Write-Output "=== TEXT CONTENT START ==="
                Get-Content $file.FullName -ErrorAction SilentlyContinue
                Write-Output "=== TEXT CONTENT END ==="
                Remove-Item $file.FullName -Force -ErrorAction SilentlyContinue
            }
        } else {
            Write-Output "No result files found"
        }
    } else {
        Write-Output "Results directory does not exist"
    }

    # Check for log files
    $logFile = "$remotePath\\ravendb_query_log.txt"
    if (Test-Path $logFile) {
        Write-Output "=== LOG FILE CONTENT ==="
        Get-Content $logFile -ErrorAction SilentlyContinue
        Write-Output "=== END LOG FILE ==="
        Remove-Item $logFile -Force -ErrorAction SilentlyContinue
    }

    # Clean up temporary files
    Remove-Item "$remotePath\\temp_query.sql" -Force -ErrorAction SilentlyContinue
    Remove-Item "$remotePath\\run_query.bat" -Force -ErrorAction SilentlyContinue

    Write-Output "Query execution completed successfully"
    exit 0

} catch {
    Write-Output "ERROR: $($_.Exception.Message)"
    exit 1
}`;

  fs.writeFileSync(psScript, scriptContent);

  // Execute the PowerShell query script
  console.log(`Executing PowerShell query script for ${restaurant.name}`);

  exec(`powershell -ExecutionPolicy Bypass -File "${psScript}"`, {
    timeout: 120000, // 2 minutes timeout for queries
    maxBuffer: 10 * 1024 * 1024 // 10MB buffer for large results
  }, (error, stdout, stderr) => {
    console.log(`Query execution output for ${restaurant.name}:`);
    console.log('STDOUT:', stdout);
    console.log('STDERR:', stderr);

    // Clean up temp files
    try {
      fs.unlinkSync(psScript);
      fs.unlinkSync(queryFile);
    } catch (e) {
      console.error('Error cleaning up temp files:', e);
    }

    if (error) {
      console.error(`Query execution error for ${restaurant.name}:`, error);
      resolve({
        success: false,
        error: `Query execution failed: ${error.message}`,
        data: []
      });
    } else {
      try {
        // Try to parse the output as JSON
        const lines = stdout.split('\n').filter(line => line.trim());
        const resultLines = lines.filter(line =>
          !line.includes('Executing query') &&
          !line.includes('Remote script found') &&
          !line.includes('Query executed successfully') &&
          line.trim() !== ''
        );

        if (resultLines.length > 0) {
          const resultText = resultLines.join('\n');

          // Try to parse as JSON first
          try {
            const jsonData = JSON.parse(resultText);
            resolve({
              success: true,
              data: Array.isArray(jsonData) ? jsonData : [jsonData],
              error: null
            });
          } catch (jsonError) {
            // If not JSON, treat as CSV or raw text
            const rows = resultText.split('\n').map(line => line.trim()).filter(line => line);
            if (rows.length > 0) {
              // Try to parse as CSV
              const data = rows.map((row, index) => {
                const columns = row.split(',').map(col => col.trim());
                if (index === 0) {
                  // First row might be headers
                  return { row: index + 1, data: columns.join(' | ') };
                } else {
                  return { row: index + 1, data: columns.join(' | ') };
                }
              });

              resolve({
                success: true,
                data: data,
                error: null
              });
            } else {
              resolve({
                success: true,
                data: [{ message: 'Query executed successfully but returned no data' }],
                error: null
              });
            }
          }
        } else {
          resolve({
            success: true,
            data: [{ message: 'Query executed successfully but returned no data' }],
            error: null
          });
        }
      } catch (parseError) {
        resolve({
          success: false,
          error: `Error parsing query results: ${parseError.message}`,
          data: []
        });
      }
    }
  });
}

/**
 * Execute query locally (legacy method)
 */
function executeQueryLocally(restaurant, query, resolve) {
  const scriptPath = path.join(app.getAppPath(), 'option1_query.bat');

  // Build command for local execution
  const command = `"${scriptPath}" -server ${restaurant.ip} -query "${query}"`;

  exec(command, { timeout: 60000 }, (error, stdout, stderr) => {
    if (error) {
      resolve({
        success: false,
        error: error.message,
        data: []
      });
    } else {
      try {
        // Parse the output (assuming it returns JSON)
        const data = stdout.trim() ? JSON.parse(stdout) : [];
        resolve({
          success: true,
          data: data,
          error: null
        });
      } catch (parseError) {
        // If not JSON, treat as raw text data
        resolve({
          success: true,
          data: [{ output: stdout.trim() }],
          error: null
        });
      }
    }
  });
}

/**
 * Execute query on selected restaurant(s) - Legacy method
 */
ipcMain.handle('execute-query', async (event, queryParams) => {
  const { restaurants, startDate, endDate, customQuery } = queryParams;

  // Create a results directory if it doesn't exist
  const resultsPath = store.get('resultFileLocation') || 'Results';
  const resultsDir = path.join(app.getAppPath(), resultsPath);
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir);
  }

  const results = [];
  const errors = [];

  for (const restaurant of restaurants) {
    try {
      // Build the command
      let command = path.join(app.getAppPath(), 'option1_query.bat');
      command = `"${command}" -server ${restaurant.ip}`;

      if (startDate) {
        command += ` -start "${startDate}"`;
      }

      if (endDate) {
        command += ` -end "${endDate}"`;
      }

      if (customQuery) {
        command += ` -query "${customQuery}"`;
      }

      // Set results path
      command += ` -results "${resultsDir}"`;

      // Execute the command
      console.log(`Executing: ${command}`);

      // Send progress update to renderer
      mainWindow.webContents.send('query-progress', {
        restaurant: restaurant.name,
        status: 'running',
        message: 'Executing query...'
      });

      // Execute command asynchronously and wait for completion
      await new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            console.error(`Error executing query for ${restaurant.name}:`, error);
            mainWindow.webContents.send('query-progress', {
              restaurant: restaurant.name,
              status: 'failed',
              error: error.message
            });

            errors.push({
              restaurant: restaurant.name,
              ip: restaurant.ip,
              error: error.message
            });

            resolve(); // Continue with other restaurants even if one fails
            return;
          }

          try {
            // Check if the result file exists
            const hostname = restaurant.ip.replace(/\./g, '-');
            const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
            const resultFile = path.join(resultsDir, `${hostname}-${date}.json`);

            if (fs.existsSync(resultFile)) {
              // Read and parse the JSON file
              const jsonData = fs.readFileSync(resultFile, 'utf8');
              let parsedData;

              try {
                parsedData = JSON.parse(jsonData);
              } catch (jsonError) {
                console.error(`Error parsing JSON for ${restaurant.name}:`, jsonError);
                mainWindow.webContents.send('query-progress', {
                  restaurant: restaurant.name,
                  status: 'parse-error',
                  error: 'Invalid JSON format in result file'
                });

                errors.push({
                  restaurant: restaurant.name,
                  ip: restaurant.ip,
                  error: 'Invalid JSON format in result file'
                });

                resolve();
                return;
              }

              // Add metadata to the result
              const result = {
                restaurant: restaurant.name,
                ip: restaurant.ip,
                data: parsedData,
                metadata: {
                  queryTime: new Date().toISOString(),
                  recordCount: parsedData.length,
                  startDate: startDate || 'Not specified',
                  endDate: endDate || 'Not specified',
                  customQuery: customQuery || 'Default query'
                }
              };

              results.push(result);

              mainWindow.webContents.send('query-progress', {
                restaurant: restaurant.name,
                status: 'completed',
                recordCount: parsedData.length
              });
            } else {
              mainWindow.webContents.send('query-progress', {
                restaurant: restaurant.name,
                status: 'no-results',
                message: 'No results file found'
              });

              errors.push({
                restaurant: restaurant.name,
                ip: restaurant.ip,
                error: 'No results file found'
              });
            }
          } catch (parseError) {
            console.error(`Error processing results for ${restaurant.name}:`, parseError);
            mainWindow.webContents.send('query-progress', {
              restaurant: restaurant.name,
              status: 'parse-error',
              error: parseError.message
            });

            errors.push({
              restaurant: restaurant.name,
              ip: restaurant.ip,
              error: parseError.message
            });
          }

          resolve();
        });
      });
    } catch (err) {
      console.error(`Error with restaurant ${restaurant.name}:`, err);
      errors.push({
        restaurant: restaurant.name,
        ip: restaurant.ip,
        error: err.message
      });
    }
  }

  // Save query to history if it has results
  if (results.length > 0) {
    ipcMain.emit('save-query', null, {
      restaurants: restaurants.map(r => ({ id: r.id, name: r.name })),
      startDate,
      endDate,
      customQuery,
      resultCount: results.length
    });
  }

  return { results, errors };
});

/**
 * Show open dialog to select a directory
 */
ipcMain.handle('select-directory', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });

  if (result.canceled) {
    return { canceled: true };
  }

  return { path: result.filePaths[0] };
});

/**
 * Show save dialog for exporting results
 */
ipcMain.handle('show-save-dialog', async (event, options) => {
  const { defaultPath, filters } = options;

  const result = await dialog.showSaveDialog(mainWindow, {
    defaultPath,
    filters
  });

  if (result.canceled) {
    return { canceled: true };
  }

  return { filePath: result.filePath };
});

/**
 * Write file
 */
ipcMain.handle('write-file', async (event, options) => {
  const { filePath, data } = options;

  try {
    fs.writeFileSync(filePath, data);
    return { success: true };
  } catch (error) {
    console.error('Error writing file:', error);
    return { error: error.message };
  }
});

/**
 * Read file
 */
ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return data;
  } catch (error) {
    console.error('Error reading file:', error);
    throw error;
  }
});

/**
 * Show open dialog
 */
ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

/**
 * Reset configuration to defaults
 */
ipcMain.handle('reset-config', () => {
  store.clear();
  return { success: true };
});

// ============================================================================
// RESTAURANT GROUPS HANDLERS
// ============================================================================

/**
 * Get restaurant groups
 */
ipcMain.handle('get-restaurant-groups', () => {
  return groupsStore.get('groups');
});

/**
 * Save restaurant group
 */
ipcMain.handle('save-restaurant-group', (event, group) => {
  const groups = groupsStore.get('groups');
  const existingIndex = groups.findIndex(g => g.id === group.id);

  if (existingIndex >= 0) {
    groups[existingIndex] = group;
  } else {
    groups.push(group);
  }

  groupsStore.set('groups', groups);
  return { success: true };
});

/**
 * Delete restaurant group
 */
ipcMain.handle('delete-restaurant-group', (event, groupId) => {
  const groups = groupsStore.get('groups');
  const filteredGroups = groups.filter(g => g.id !== groupId);
  groupsStore.set('groups', filteredGroups);
  return { success: true };
});

// ============================================================================
// CONNECTION TESTING HANDLERS
// ============================================================================

/**
 * Test connections to restaurants
 */
ipcMain.handle('test-connections', async (event, restaurants) => {
  const results = [];

  for (const restaurant of restaurants) {
    try {
      // Send progress update
      mainWindow.webContents.send('connection-test-progress', {
        restaurant: restaurant.name,
        status: 'testing'
      });

      // Test connection using ping or PowerShell Test-NetConnection
      const testResult = await testConnection(restaurant.ip);

      results.push({
        restaurantId: restaurant.id,
        status: testResult ? 'online' : 'offline',
        responseTime: testResult?.responseTime || null
      });

      // Send progress update
      mainWindow.webContents.send('connection-test-progress', {
        restaurant: restaurant.name,
        status: testResult ? 'online' : 'offline'
      });
    } catch (error) {
      console.error(`Error testing connection to ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        status: 'offline',
        error: error.message
      });
    }
  }

  return results;
});

/**
 * Test connection to a single IP
 */
async function testConnection(ip) {
  return new Promise((resolve) => {
    const startTime = Date.now();

    // Use PowerShell Test-NetConnection for more reliable testing
    const command = `powershell -Command "Test-NetConnection -ComputerName ${ip} -Port 445 -InformationLevel Quiet"`;

    exec(command, { timeout: 10000 }, (error, stdout, stderr) => {
      const responseTime = Date.now() - startTime;

      if (error) {
        resolve(false);
      } else {
        const isOnline = stdout.trim().toLowerCase() === 'true';
        resolve(isOnline ? { responseTime } : false);
      }
    });
  });
}

// ============================================================================
// DEPLOYMENT HANDLERS
// ============================================================================

/**
 * Get deployment status
 */
ipcMain.handle('get-deployment-status', () => {
  return deploymentStore.get('status');
});

/**
 * Save deployment status
 */
ipcMain.handle('save-deployment-status', (event, status) => {
  deploymentStore.set('status', status);
  return { success: true };
});

/**
 * Deploy script to restaurants
 */
ipcMain.handle('deploy-script', async (event, options) => {
  const { restaurants, config } = options;
  const results = [];

  for (const restaurant of restaurants) {
    try {
      // Send progress update
      mainWindow.webContents.send('deployment-progress', {
        restaurant: restaurant.name,
        status: 'deploying'
      });

      const deployResult = await deployToRestaurant(restaurant, config);

      results.push({
        restaurantId: restaurant.id,
        success: deployResult.success,
        error: deployResult.error,
        details: deployResult.details
      });

      // Send progress update
      mainWindow.webContents.send('deployment-progress', {
        restaurant: restaurant.name,
        status: deployResult.success ? 'deployed' : 'failed',
        error: deployResult.error
      });
    } catch (error) {
      console.error(`Error deploying to ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        success: false,
        error: error.message
      });
    }
  }

  return results;
});

/**
 * Deploy script to a single restaurant
 */
async function deployToRestaurant(restaurant, config) {
  return new Promise((resolve) => {
    const { targetPath, username, password, createFolder, backup } = config;

    // Try simple file copy first (for testing)
    if (process.env.NODE_ENV === 'development' || config.useSimpleDeployment) {
      return deployToRestaurantSimple(restaurant, config).then(resolve);
    }

    // Create PowerShell script for deployment
    const psScript = path.join(app.getAppPath(), 'temp_deploy.ps1');

    // Convert boolean values to PowerShell format
    const createFolderPS = createFolder ? '$true' : '$false';
    const backupPS = backup ? '$true' : '$false';

    // Get the local script path
    const localScriptPath = path.join(app.getAppPath(), 'option1_query.bat').replace(/\\/g, '\\\\');

    let scriptContent = `
$ErrorActionPreference = "Stop"
try {
    # Connection settings
    $securePassword = ConvertTo-SecureString "${password}" -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential ("${username}", $securePassword)

    # Test connection first
    Write-Output "Testing connection to ${restaurant.ip}..."
    if (-not (Test-NetConnection -ComputerName ${restaurant.ip} -Port 5985 -InformationLevel Quiet)) {
        throw "Cannot connect to ${restaurant.ip} on WinRM port 5985"
    }

    $session = New-PSSession -ComputerName ${restaurant.ip} -Credential $credential -ErrorAction Stop
    Write-Output "PowerShell session established"

    # Execute deployment commands remotely
    Invoke-Command -Session $session -ScriptBlock {
        param($targetPath, $createFolder, $backup)

        $scriptPath = Join-Path $targetPath "option1_query.bat"

        # Create directory if it doesn't exist
        if (-not (Test-Path $targetPath)) {
            if ($createFolder) {
                New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
                Write-Output "Created directory: $targetPath"
            } else {
                throw "Target directory does not exist: $targetPath"
            }
        }

        # Backup existing file if requested
        if ((Test-Path $scriptPath) -and $backup) {
            $backupPath = "$scriptPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            Copy-Item $scriptPath $backupPath -Force
            Write-Output "Backed up existing file to: $backupPath"
        }

        Write-Output "Deployment target ready: $targetPath"
    } -ArgumentList "${targetPath}", ${createFolderPS}, ${backupPS}

    # Copy the script file
    $localScript = "${localScriptPath}"
    $remotePath = "\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools\\option1_query.bat"

    if (-not (Test-Path $localScript)) {
        throw "Local script file not found: $localScript"
    }

    Write-Output "Copying script from $localScript to $remotePath"
    Copy-Item -Path $localScript -Destination $remotePath -Force
    Write-Output "Script deployed successfully"

    # Clean up
    Remove-PSSession $session
    Write-Output "Deployment completed successfully"
    exit 0
} catch {
    Write-Output "ERROR: $($_.Exception.Message)"
    if ($session) {
        Remove-PSSession $session -ErrorAction SilentlyContinue
    }
    exit 1
}
`;

    fs.writeFileSync(psScript, scriptContent);

    // Execute the PowerShell script with better error handling
    console.log(`Executing deployment script for ${restaurant.name} (${restaurant.ip})`);
    console.log(`Script path: ${psScript}`);

    exec(`powershell -ExecutionPolicy Bypass -File "${psScript}"`, {
      timeout: 120000,  // Increased timeout to 2 minutes
      maxBuffer: 1024 * 1024  // 1MB buffer for output
    }, (error, stdout, stderr) => {
      console.log(`Deployment output for ${restaurant.name}:`);
      console.log('STDOUT:', stdout);
      console.log('STDERR:', stderr);

      // Clean up temp script
      try {
        fs.unlinkSync(psScript);
      } catch (e) {
        console.error('Error cleaning up temp script:', e);
      }

      if (error) {
        console.error(`Deployment error for ${restaurant.name}:`, error);
        resolve({
          success: false,
          error: `PowerShell execution failed: ${error.message}`,
          details: `STDOUT: ${stdout}\nSTDERR: ${stderr}\nError: ${error.message}`
        });
      } else {
        // Check if the output contains any error indicators
        const output = stdout + stderr;
        if (output.toLowerCase().includes('error:') || output.toLowerCase().includes('exception')) {
          resolve({
            success: false,
            error: 'Deployment script reported errors',
            details: output
          });
        } else {
          resolve({
            success: true,
            details: stdout
          });
        }
      }
    });
  });
}

/**
 * Simple deployment method using direct file copy
 */
async function deployToRestaurantSimple(restaurant, config) {
  return new Promise((resolve) => {
    const { targetPath, createFolder, backup } = config;

    console.log(`Attempting simple deployment to ${restaurant.name} (${restaurant.ip})`);

    // Create the remote path
    const remotePath = `\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools`;
    const remoteScriptPath = path.join(remotePath, 'option1_query.bat');
    const localScriptPath = path.join(app.getAppPath(), 'option1_query.bat');

    // Create PowerShell script for simple deployment
    const psScript = path.join(app.getAppPath(), 'temp_simple_deploy.ps1');

    let scriptContent = `
$ErrorActionPreference = "Stop"
try {
    $remotePath = "${remotePath}"
    $remoteScriptPath = "${remoteScriptPath.replace(/\\/g, '\\\\')}"
    $localScriptPath = "${localScriptPath.replace(/\\/g, '\\\\')}"

    Write-Output "Testing connection to ${restaurant.ip}..."

    # Test if we can access the remote path
    if (-not (Test-Path "\\\\${restaurant.ip}\\D$")) {
        throw "Cannot access \\\\${restaurant.ip}\\D$ - check network connectivity and permissions"
    }

    Write-Output "Network path accessible"

    # Create directory structure if needed
    if (-not (Test-Path $remotePath)) {
        if (${createFolder ? '$true' : '$false'}) {
            New-Item -ItemType Directory -Path $remotePath -Force | Out-Null
            Write-Output "Created directory: $remotePath"
        } else {
            throw "Target directory does not exist: $remotePath"
        }
    }

    # Backup existing file if requested
    if ((Test-Path $remoteScriptPath) -and ${backup ? '$true' : '$false'}) {
        $backupPath = "$remoteScriptPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $remoteScriptPath $backupPath -Force
        Write-Output "Backed up existing file to: $backupPath"
    }

    # Copy the script file
    if (-not (Test-Path $localScriptPath)) {
        throw "Local script file not found: $localScriptPath"
    }

    Write-Output "Copying script from $localScriptPath to $remoteScriptPath"
    Copy-Item -Path $localScriptPath -Destination $remoteScriptPath -Force

    # Verify the copy was successful
    if (Test-Path $remoteScriptPath) {
        Write-Output "Script deployed successfully to ${restaurant.ip}"
        Write-Output "Deployment completed successfully"
    } else {
        throw "Failed to copy script to remote location"
    }

    exit 0
} catch {
    Write-Output "ERROR: $($_.Exception.Message)"
    exit 1
}
`;

    fs.writeFileSync(psScript, scriptContent);

    // Execute the simple deployment script
    console.log(`Executing simple deployment script for ${restaurant.name}`);

    exec(`powershell -ExecutionPolicy Bypass -File "${psScript}"`, {
      timeout: 60000,
      maxBuffer: 1024 * 1024
    }, (error, stdout, stderr) => {
      console.log(`Simple deployment output for ${restaurant.name}:`);
      console.log('STDOUT:', stdout);
      console.log('STDERR:', stderr);

      // Clean up temp script
      try {
        fs.unlinkSync(psScript);
      } catch (e) {
        console.error('Error cleaning up temp script:', e);
      }

      if (error) {
        console.error(`Simple deployment error for ${restaurant.name}:`, error);
        resolve({
          success: false,
          error: `Simple deployment failed: ${error.message}`,
          details: `STDOUT: ${stdout}\nSTDERR: ${stderr}\nError: ${error.message}`
        });
      } else {
        const output = stdout + stderr;
        if (output.toLowerCase().includes('error:') || output.toLowerCase().includes('exception')) {
          resolve({
            success: false,
            error: 'Simple deployment script reported errors',
            details: output
          });
        } else {
          resolve({
            success: true,
            details: stdout
          });
        }
      }
    });
  });
}

/**
 * Simple deployment method using batch file
 */
async function deployToRestaurantSimple(restaurant, config) {
  return new Promise((resolve) => {
    const { targetPath, createFolder, backup } = config;

    console.log(`Attempting batch deployment to ${restaurant.name} (${restaurant.ip})`);

    // Create the remote path
    const remotePath = `\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools`;
    const remoteScriptPath = path.join(remotePath, 'option1_query.bat');
    const localScriptPath = path.join(app.getAppPath(), 'option1_query.bat');

    // Create batch script for deployment
    const batchScript = path.join(app.getAppPath(), 'temp_deploy.bat');

    // Fix paths for batch file (escape quotes and handle spaces)
    const remotePathFixed = remotePath.replace(/\\/g, '\\');
    const remoteScriptPathFixed = remoteScriptPath.replace(/\\/g, '\\');
    const localScriptPathFixed = localScriptPath.replace(/\\/g, '\\');

    let scriptContent = `@echo off
setlocal enabledelayedexpansion

echo Testing connection to ${restaurant.ip}...

REM Test if we can access the remote path
if not exist "\\\\${restaurant.ip}\\D$" (
    echo ERROR: Cannot access \\\\${restaurant.ip}\\D$ - check network connectivity and permissions
    exit /b 1
)

echo Network path accessible

REM Create directory structure if needed
if not exist "${remotePathFixed}" (
    if "${createFolder}" == "true" (
        mkdir "${remotePathFixed}" 2>nul
        if !errorlevel! == 0 (
            echo Created directory: ${remotePathFixed}
        ) else (
            echo ERROR: Failed to create directory: ${remotePathFixed}
            exit /b 1
        )
    ) else (
        echo ERROR: Target directory does not exist: ${remotePathFixed}
        exit /b 1
    )
)

REM Backup existing file if requested
if exist "${remoteScriptPathFixed}" (
    if "${backup}" == "true" (
        set timestamp=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
        set timestamp=!timestamp: =0!
        copy "${remoteScriptPathFixed}" "${remoteScriptPathFixed}.backup.!timestamp!" >nul 2>nul
        if !errorlevel! == 0 (
            echo Backed up existing file to: ${remoteScriptPathFixed}.backup.!timestamp!
        )
    )
)

REM Check if local script exists
if not exist "${localScriptPathFixed}" (
    echo ERROR: Local script file not found: ${localScriptPathFixed}
    exit /b 1
)

echo Copying script from "${localScriptPathFixed}" to "${remoteScriptPathFixed}"
copy "${localScriptPathFixed}" "${remoteScriptPathFixed}" >nul 2>nul

if !errorlevel! == 0 (
    echo Script deployed successfully to ${restaurant.ip}
    echo Deployment completed successfully
    exit /b 0
) else (
    echo ERROR: Failed to copy script to remote location
    echo Trying alternative copy method...
    xcopy "${localScriptPathFixed}" "${remotePathFixed}\\" /Y >nul 2>nul
    if !errorlevel! == 0 (
        echo Script deployed successfully using xcopy
        echo Deployment completed successfully
        exit /b 0
    ) else (
        echo ERROR: All copy methods failed
        exit /b 1
    )
)
`;

    fs.writeFileSync(batchScript, scriptContent);

    // Execute the batch deployment script
    console.log(`Executing batch deployment script for ${restaurant.name}`);

    exec(`"${batchScript}"`, {
      timeout: 60000,
      maxBuffer: 1024 * 1024
    }, (error, stdout, stderr) => {
      console.log(`Batch deployment output for ${restaurant.name}:`);
      console.log('STDOUT:', stdout);
      console.log('STDERR:', stderr);

      // Clean up temp script
      try {
        fs.unlinkSync(batchScript);
      } catch (e) {
        console.error('Error cleaning up temp script:', e);
      }

      if (error) {
        console.error(`Batch deployment error for ${restaurant.name}:`, error);
        resolve({
          success: false,
          error: `Batch deployment failed: ${error.message}`,
          details: `STDOUT: ${stdout}\nSTDERR: ${stderr}\nError: ${error.message}`
        });
      } else {
        const output = stdout + stderr;
        if (output.toLowerCase().includes('error:')) {
          resolve({
            success: false,
            error: 'Batch deployment script reported errors',
            details: output
          });
        } else {
          resolve({
            success: true,
            details: stdout
          });
        }
      }
    });
  });
}

/**
 * Clean up temporary files on restaurants
 */
ipcMain.handle('cleanup-temp-files', async (event, restaurants) => {
  const results = [];

  for (const restaurant of restaurants) {
    try {
      const remotePath = `\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools`;
      const tempFiles = [
        `${remotePath}\\query_results.txt`,
        `${remotePath}\\temp_query.sql`,
        `${remotePath}\\error.log`
      ];

      let cleanedFiles = 0;
      for (const file of tempFiles) {
        try {
          if (fs.existsSync(file)) {
            fs.unlinkSync(file);
            cleanedFiles++;
          }
        } catch (e) {
          // Ignore individual file cleanup errors
        }
      }

      results.push({
        restaurantId: restaurant.id,
        restaurant: restaurant.name,
        ip: restaurant.ip,
        success: true,
        cleanedFiles: cleanedFiles
      });
    } catch (error) {
      results.push({
        restaurantId: restaurant.id,
        restaurant: restaurant.name,
        ip: restaurant.ip,
        success: false,
        error: error.message
      });
    }
  }

  return results;
});

/**
 * Check deployment status on restaurants
 */
ipcMain.handle('check-deployment-status', async (event, restaurants) => {
  const results = [];

  for (const restaurant of restaurants) {
    try {
      const statusResult = await checkDeploymentOnRestaurant(restaurant);
      results.push({
        restaurantId: restaurant.id,
        deployed: statusResult.deployed,
        version: statusResult.version,
        lastModified: statusResult.lastModified,
        error: statusResult.error
      });
    } catch (error) {
      console.error(`Error checking deployment on ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        deployed: false,
        error: error.message
      });
    }
  }

  return results;
});

/**
 * Check deployment status on a single restaurant
 */
async function checkDeploymentOnRestaurant(restaurant) {
  return new Promise((resolve) => {
    const command = `powershell -Command "Test-Path '\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools\\option1_query.bat'"`;

    exec(command, { timeout: 15000 }, (error, stdout, stderr) => {
      if (error) {
        resolve({
          deployed: false,
          error: error.message
        });
      } else {
        const deployed = stdout.trim().toLowerCase() === 'true';
        resolve({
          deployed: deployed,
          version: deployed ? '1.0.0' : null,
          lastModified: deployed ? new Date().toISOString() : null
        });
      }
    });
  });
}

/**
 * Remove deployments from restaurants
 */
ipcMain.handle('remove-deployments', async (event, restaurants) => {
  const results = [];

  for (const restaurant of restaurants) {
    try {
      const removeResult = await removeDeploymentFromRestaurant(restaurant);
      results.push({
        restaurantId: restaurant.id,
        success: removeResult.success,
        error: removeResult.error
      });
    } catch (error) {
      console.error(`Error removing deployment from ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        success: false,
        error: error.message
      });
    }
  }

  return results;
});

/**
 * Remove deployment from a single restaurant
 */
async function removeDeploymentFromRestaurant(restaurant) {
  return new Promise((resolve) => {
    const command = `powershell -Command "Remove-Item '\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools\\option1_query.bat' -Force -ErrorAction SilentlyContinue"`;

    exec(command, { timeout: 15000 }, (error, stdout, stderr) => {
      if (error) {
        resolve({
          success: false,
          error: error.message
        });
      } else {
        resolve({
          success: true
        });
      }
    });
  });
}