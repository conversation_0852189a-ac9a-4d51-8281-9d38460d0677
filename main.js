const { app, BrowserWindow, ipcMain, dialog, Menu, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const Store = require('electron-store');

// Try to enable hot reload for development
try {
  require('electron-reloader')(module, {
    ignore: ['src/renderer/js/config.js']
  });
} catch {}

// Initialize configuration store
const store = new Store({
  name: 'config',
  defaults: {
    windowSize: { width: 1200, height: 800 },
    lastQueries: [],
    maxSavedQueries: 10,
    defaultResultsLimit: 20,
    resultFileLocation: 'Results',
    queryTimeout: 30,
    maxConcurrent: 3,
    defaultUsername: 'Administrator',
    defaultPassword: 'Jvr963*14',
    connectionTimeout: 15,
    autoRetry: true,
    logLevel: 'info',
    backupRetention: 30,
    enableNotifications: true,
    autoSaveResults: true
  }
});

// Initialize additional stores
const groupsStore = new Store({
  name: 'restaurant-groups',
  defaults: {
    groups: []
  }
});

const deploymentStore = new Store({
  name: 'deployment-status',
  defaults: {
    status: {}
  }
});

// Global reference to mainWindow to prevent garbage collection
let mainWindow;

/**
 * Create the main application window
 */
function createWindow() {
  // Get saved window size or use default
  const { width, height } = store.get('windowSize');

  // Create the browser window
  mainWindow = new BrowserWindow({
    width,
    height,
    title: "McDonald's Restaurant Query Tool",
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    show: false, // Don't show until ready-to-show
    icon: path.join(__dirname, './assets/icon.png')
  });

  // Load the index.html file
  mainWindow.loadFile(path.join(__dirname, './index.html'));

  // Open DevTools in development mode
  if (process.argv.includes('--debug')) {
    mainWindow.webContents.openDevTools();
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Save window size on close
  mainWindow.on('close', () => {
    store.set('windowSize', mainWindow.getBounds());
  });

  // Create application menu
  createAppMenu();

  return mainWindow;
}

/**
 * Create the application menu
 */
function createAppMenu() {
  const isMac = process.platform === 'darwin';

  const template = [
    ...(isMac ? [{
      label: app.name,
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideothers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    }] : []),
    {
      label: 'File',
      submenu: [
        {
          label: 'Export Results',
          accelerator: 'CmdOrCtrl+E',
          click: () => {
            mainWindow.webContents.send('menu-export-results');
          }
        },
        { type: 'separator' },
        isMac ? { role: 'close' } : { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'delete' },
        { type: 'separator' },
        { role: 'selectAll' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'Documentation',
          click: async () => {
            const docPath = path.join(__dirname, './user-guide.pdf');
            if (fs.existsSync(docPath)) {
              shell.openPath(docPath);
            } else {
              dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: 'Documentation',
                message: 'Documentation file not found.',
                detail: 'Please contact support for assistance.'
              });
            }
          }
        },
        {
          label: 'About',
          click: async () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About',
              message: 'McDonald\'s Restaurant Query Tool',
              detail: 'Version 1.0.0\n\nA tool for running queries on restaurant servers.'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Initialize the app
app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    // On macOS re-create a window when the dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

/**
 * Load restaurants from file
 */
ipcMain.handle('read-restaurants', async () => {
  try {
    const filePath = path.join(app.getAppPath(), 'Restaurants.txt');
    if (!fs.existsSync(filePath)) {
      return { error: 'Restaurants.txt file not found. Please place it in the application directory.' };
    }

    const data = fs.readFileSync(filePath, 'utf8');
    const lines = data.split('\n');

    const restaurants = [];
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line || line.startsWith('EID') || line.startsWith('MA')) continue;

      // Extract EID, NAME, IP_BO
      const parts = line.split('\t').filter(part => part.trim() !== '');
      if (parts.length >= 3) {
        const id = parts[0].trim();
        const name = parts[1].trim();
        const ip = parts[2].trim();

        // Skip empty entries
        if (id && name && ip) {
          restaurants.push({
            id: id,
            name: name,
            ip: ip
          });
        }
      }
    }

    // If no restaurants were found
    if (restaurants.length === 0) {
      return { error: 'No valid restaurant entries found in Restaurants.txt' };
    }

    return { restaurants };
  } catch (error) {
    console.error('Error reading restaurants file:', error);
    return { error: `Error reading restaurants file: ${error.message}` };
  }
});

/**
 * Get configuration
 */
ipcMain.handle('get-config', () => {
  return store.store;
});

/**
 * Set configuration
 */
ipcMain.handle('set-config', (event, config) => {
  Object.keys(config).forEach(key => {
    store.set(key, config[key]);
  });
  return { success: true };
});

/**
 * Save query to history
 */
ipcMain.handle('save-query', (event, query) => {
  const maxQueries = store.get('maxSavedQueries');
  let queries = store.get('lastQueries') || [];

  // Add timestamp to query
  query.timestamp = new Date().toISOString();

  // Add to beginning of array
  queries.unshift(query);

  // Limit to max number of queries
  if (queries.length > maxQueries) {
    queries = queries.slice(0, maxQueries);
  }

  store.set('lastQueries', queries);
  return { success: true };
});

/**
 * Execute query on selected restaurant(s) using deployed script
 */
ipcMain.handle('execute-query-with-deployment', async (event, queryParams) => {
  const { restaurants, query, useDeployedScript } = queryParams;
  const results = [];

  for (const restaurant of restaurants) {
    try {
      // Send progress update
      mainWindow.webContents.send('query-progress', {
        restaurant: restaurant.name,
        status: 'running',
        message: 'Executing query using deployed script...'
      });

      const queryResult = await executeQueryOnRestaurant(restaurant, query, useDeployedScript);

      results.push({
        restaurantId: restaurant.id,
        restaurant: restaurant.name,
        ip: restaurant.ip,
        success: queryResult.success,
        data: queryResult.data || [],
        error: queryResult.error,
        metadata: {
          queryTime: new Date().toISOString(),
          query: query,
          useDeployedScript: useDeployedScript
        }
      });

      // Send progress update
      mainWindow.webContents.send('query-progress', {
        restaurant: restaurant.name,
        status: queryResult.success ? 'completed' : 'failed',
        error: queryResult.error
      });
    } catch (error) {
      console.error(`Error executing query on ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        restaurant: restaurant.name,
        ip: restaurant.ip,
        success: false,
        data: [],
        error: error.message
      });
    }
  }

  return { results, errors: results.filter(r => !r.success) };
});

/**
 * Execute query on a single restaurant using deployed script
 */
async function executeQueryOnRestaurant(restaurant, query, useDeployedScript) {
  return new Promise((resolve) => {
    const scriptPath = useDeployedScript
      ? `\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools\\option1_query.bat`
      : path.join(app.getAppPath(), 'option1_query.bat');

    // Build PowerShell command to execute the script remotely
    const command = `powershell -Command "Invoke-Command -ComputerName ${restaurant.ip} -ScriptBlock { & 'D:\\Newpos61\\Support_Tools\\Ultimate_support_tools\\option1_query.bat' '${query}' }"`;

    exec(command, { timeout: 60000 }, (error, stdout, stderr) => {
      if (error) {
        resolve({
          success: false,
          error: error.message,
          data: []
        });
      } else {
        try {
          // Parse the output (assuming it returns JSON)
          const data = stdout.trim() ? JSON.parse(stdout) : [];
          resolve({
            success: true,
            data: data,
            error: null
          });
        } catch (parseError) {
          // If not JSON, treat as raw text data
          resolve({
            success: true,
            data: [{ output: stdout.trim() }],
            error: null
          });
        }
      }
    });
  });
}

/**
 * Execute query on selected restaurant(s) - Legacy method
 */
ipcMain.handle('execute-query', async (event, queryParams) => {
  const { restaurants, startDate, endDate, customQuery } = queryParams;

  // Create a results directory if it doesn't exist
  const resultsPath = store.get('resultFileLocation') || 'Results';
  const resultsDir = path.join(app.getAppPath(), resultsPath);
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir);
  }

  const results = [];
  const errors = [];

  for (const restaurant of restaurants) {
    try {
      // Build the command
      let command = path.join(app.getAppPath(), 'option1_query.bat');
      command = `"${command}" -server ${restaurant.ip}`;

      if (startDate) {
        command += ` -start "${startDate}"`;
      }

      if (endDate) {
        command += ` -end "${endDate}"`;
      }

      if (customQuery) {
        command += ` -query "${customQuery}"`;
      }

      // Set results path
      command += ` -results "${resultsDir}"`;

      // Execute the command
      console.log(`Executing: ${command}`);

      // Send progress update to renderer
      mainWindow.webContents.send('query-progress', {
        restaurant: restaurant.name,
        status: 'running',
        message: 'Executing query...'
      });

      // Execute command asynchronously and wait for completion
      await new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
          if (error) {
            console.error(`Error executing query for ${restaurant.name}:`, error);
            mainWindow.webContents.send('query-progress', {
              restaurant: restaurant.name,
              status: 'failed',
              error: error.message
            });

            errors.push({
              restaurant: restaurant.name,
              ip: restaurant.ip,
              error: error.message
            });

            resolve(); // Continue with other restaurants even if one fails
            return;
          }

          try {
            // Check if the result file exists
            const hostname = restaurant.ip.replace(/\./g, '-');
            const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
            const resultFile = path.join(resultsDir, `${hostname}-${date}.json`);

            if (fs.existsSync(resultFile)) {
              // Read and parse the JSON file
              const jsonData = fs.readFileSync(resultFile, 'utf8');
              let parsedData;

              try {
                parsedData = JSON.parse(jsonData);
              } catch (jsonError) {
                console.error(`Error parsing JSON for ${restaurant.name}:`, jsonError);
                mainWindow.webContents.send('query-progress', {
                  restaurant: restaurant.name,
                  status: 'parse-error',
                  error: 'Invalid JSON format in result file'
                });

                errors.push({
                  restaurant: restaurant.name,
                  ip: restaurant.ip,
                  error: 'Invalid JSON format in result file'
                });

                resolve();
                return;
              }

              // Add metadata to the result
              const result = {
                restaurant: restaurant.name,
                ip: restaurant.ip,
                data: parsedData,
                metadata: {
                  queryTime: new Date().toISOString(),
                  recordCount: parsedData.length,
                  startDate: startDate || 'Not specified',
                  endDate: endDate || 'Not specified',
                  customQuery: customQuery || 'Default query'
                }
              };

              results.push(result);

              mainWindow.webContents.send('query-progress', {
                restaurant: restaurant.name,
                status: 'completed',
                recordCount: parsedData.length
              });
            } else {
              mainWindow.webContents.send('query-progress', {
                restaurant: restaurant.name,
                status: 'no-results',
                message: 'No results file found'
              });

              errors.push({
                restaurant: restaurant.name,
                ip: restaurant.ip,
                error: 'No results file found'
              });
            }
          } catch (parseError) {
            console.error(`Error processing results for ${restaurant.name}:`, parseError);
            mainWindow.webContents.send('query-progress', {
              restaurant: restaurant.name,
              status: 'parse-error',
              error: parseError.message
            });

            errors.push({
              restaurant: restaurant.name,
              ip: restaurant.ip,
              error: parseError.message
            });
          }

          resolve();
        });
      });
    } catch (err) {
      console.error(`Error with restaurant ${restaurant.name}:`, err);
      errors.push({
        restaurant: restaurant.name,
        ip: restaurant.ip,
        error: err.message
      });
    }
  }

  // Save query to history if it has results
  if (results.length > 0) {
    ipcMain.emit('save-query', null, {
      restaurants: restaurants.map(r => ({ id: r.id, name: r.name })),
      startDate,
      endDate,
      customQuery,
      resultCount: results.length
    });
  }

  return { results, errors };
});

/**
 * Show open dialog to select a directory
 */
ipcMain.handle('select-directory', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory']
  });

  if (result.canceled) {
    return { canceled: true };
  }

  return { path: result.filePaths[0] };
});

/**
 * Show save dialog for exporting results
 */
ipcMain.handle('show-save-dialog', async (event, options) => {
  const { defaultPath, filters } = options;

  const result = await dialog.showSaveDialog(mainWindow, {
    defaultPath,
    filters
  });

  if (result.canceled) {
    return { canceled: true };
  }

  return { filePath: result.filePath };
});

/**
 * Write file
 */
ipcMain.handle('write-file', async (event, options) => {
  const { filePath, data } = options;

  try {
    fs.writeFileSync(filePath, data);
    return { success: true };
  } catch (error) {
    console.error('Error writing file:', error);
    return { error: error.message };
  }
});

/**
 * Read file
 */
ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return data;
  } catch (error) {
    console.error('Error reading file:', error);
    throw error;
  }
});

/**
 * Show open dialog
 */
ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

/**
 * Reset configuration to defaults
 */
ipcMain.handle('reset-config', () => {
  store.clear();
  return { success: true };
});

// ============================================================================
// RESTAURANT GROUPS HANDLERS
// ============================================================================

/**
 * Get restaurant groups
 */
ipcMain.handle('get-restaurant-groups', () => {
  return groupsStore.get('groups');
});

/**
 * Save restaurant group
 */
ipcMain.handle('save-restaurant-group', (event, group) => {
  const groups = groupsStore.get('groups');
  const existingIndex = groups.findIndex(g => g.id === group.id);

  if (existingIndex >= 0) {
    groups[existingIndex] = group;
  } else {
    groups.push(group);
  }

  groupsStore.set('groups', groups);
  return { success: true };
});

/**
 * Delete restaurant group
 */
ipcMain.handle('delete-restaurant-group', (event, groupId) => {
  const groups = groupsStore.get('groups');
  const filteredGroups = groups.filter(g => g.id !== groupId);
  groupsStore.set('groups', filteredGroups);
  return { success: true };
});

// ============================================================================
// CONNECTION TESTING HANDLERS
// ============================================================================

/**
 * Test connections to restaurants
 */
ipcMain.handle('test-connections', async (event, restaurants) => {
  const results = [];

  for (const restaurant of restaurants) {
    try {
      // Send progress update
      mainWindow.webContents.send('connection-test-progress', {
        restaurant: restaurant.name,
        status: 'testing'
      });

      // Test connection using ping or PowerShell Test-NetConnection
      const testResult = await testConnection(restaurant.ip);

      results.push({
        restaurantId: restaurant.id,
        status: testResult ? 'online' : 'offline',
        responseTime: testResult?.responseTime || null
      });

      // Send progress update
      mainWindow.webContents.send('connection-test-progress', {
        restaurant: restaurant.name,
        status: testResult ? 'online' : 'offline'
      });
    } catch (error) {
      console.error(`Error testing connection to ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        status: 'offline',
        error: error.message
      });
    }
  }

  return results;
});

/**
 * Test connection to a single IP
 */
async function testConnection(ip) {
  return new Promise((resolve) => {
    const startTime = Date.now();

    // Use PowerShell Test-NetConnection for more reliable testing
    const command = `powershell -Command "Test-NetConnection -ComputerName ${ip} -Port 445 -InformationLevel Quiet"`;

    exec(command, { timeout: 10000 }, (error, stdout, stderr) => {
      const responseTime = Date.now() - startTime;

      if (error) {
        resolve(false);
      } else {
        const isOnline = stdout.trim().toLowerCase() === 'true';
        resolve(isOnline ? { responseTime } : false);
      }
    });
  });
}

// ============================================================================
// DEPLOYMENT HANDLERS
// ============================================================================

/**
 * Get deployment status
 */
ipcMain.handle('get-deployment-status', () => {
  return deploymentStore.get('status');
});

/**
 * Save deployment status
 */
ipcMain.handle('save-deployment-status', (event, status) => {
  deploymentStore.set('status', status);
  return { success: true };
});

/**
 * Deploy script to restaurants
 */
ipcMain.handle('deploy-script', async (event, options) => {
  const { restaurants, config } = options;
  const results = [];

  for (const restaurant of restaurants) {
    try {
      // Send progress update
      mainWindow.webContents.send('deployment-progress', {
        restaurant: restaurant.name,
        status: 'deploying'
      });

      const deployResult = await deployToRestaurant(restaurant, config);

      results.push({
        restaurantId: restaurant.id,
        success: deployResult.success,
        error: deployResult.error,
        details: deployResult.details
      });

      // Send progress update
      mainWindow.webContents.send('deployment-progress', {
        restaurant: restaurant.name,
        status: deployResult.success ? 'deployed' : 'failed',
        error: deployResult.error
      });
    } catch (error) {
      console.error(`Error deploying to ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        success: false,
        error: error.message
      });
    }
  }

  return results;
});

/**
 * Deploy script to a single restaurant
 */
async function deployToRestaurant(restaurant, config) {
  return new Promise((resolve) => {
    const { targetPath, username, password, createFolder, backup } = config;

    // Create PowerShell script for deployment
    const psScript = path.join(app.getAppPath(), 'temp_deploy.ps1');

    // Convert boolean values to PowerShell format
    const createFolderPS = createFolder ? '$true' : '$false';
    const backupPS = backup ? '$true' : '$false';

    // Get the local script path
    const localScriptPath = path.join(app.getAppPath(), 'option1_query.bat').replace(/\\/g, '\\\\');

    let scriptContent = `
$ErrorActionPreference = "Stop"
try {
    # Connection settings
    $securePassword = ConvertTo-SecureString "${password}" -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential ("${username}", $securePassword)

    # Test connection first
    Write-Output "Testing connection to ${restaurant.ip}..."
    if (-not (Test-NetConnection -ComputerName ${restaurant.ip} -Port 5985 -InformationLevel Quiet)) {
        throw "Cannot connect to ${restaurant.ip} on WinRM port 5985"
    }

    $session = New-PSSession -ComputerName ${restaurant.ip} -Credential $credential -ErrorAction Stop
    Write-Output "PowerShell session established"

    # Execute deployment commands remotely
    Invoke-Command -Session $session -ScriptBlock {
        param($targetPath, $createFolder, $backup)

        $scriptPath = Join-Path $targetPath "option1_query.bat"

        # Create directory if it doesn't exist
        if (-not (Test-Path $targetPath)) {
            if ($createFolder) {
                New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
                Write-Output "Created directory: $targetPath"
            } else {
                throw "Target directory does not exist: $targetPath"
            }
        }

        # Backup existing file if requested
        if ((Test-Path $scriptPath) -and $backup) {
            $backupPath = "$scriptPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            Copy-Item $scriptPath $backupPath -Force
            Write-Output "Backed up existing file to: $backupPath"
        }

        Write-Output "Deployment target ready: $targetPath"
    } -ArgumentList "${targetPath}", ${createFolderPS}, ${backupPS}

    # Copy the script file
    $localScript = "${localScriptPath}"
    $remotePath = "\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools\\option1_query.bat"

    if (-not (Test-Path $localScript)) {
        throw "Local script file not found: $localScript"
    }

    Write-Output "Copying script from $localScript to $remotePath"
    Copy-Item -Path $localScript -Destination $remotePath -Force
    Write-Output "Script deployed successfully"

    # Clean up
    Remove-PSSession $session
    Write-Output "Deployment completed successfully"
    exit 0
} catch {
    Write-Output "ERROR: $($_.Exception.Message)"
    if ($session) {
        Remove-PSSession $session -ErrorAction SilentlyContinue
    }
    exit 1
}
`;

    fs.writeFileSync(psScript, scriptContent);

    // Execute the PowerShell script
    exec(`powershell -ExecutionPolicy Bypass -File "${psScript}"`, { timeout: 60000 }, (error, stdout, stderr) => {
      // Clean up temp script
      try {
        fs.unlinkSync(psScript);
      } catch (e) {
        console.error('Error cleaning up temp script:', e);
      }

      if (error) {
        resolve({
          success: false,
          error: error.message,
          details: stdout + stderr
        });
      } else {
        resolve({
          success: true,
          details: stdout
        });
      }
    });
  });
}

/**
 * Check deployment status on restaurants
 */
ipcMain.handle('check-deployment-status', async (event, restaurants) => {
  const results = [];

  for (const restaurant of restaurants) {
    try {
      const statusResult = await checkDeploymentOnRestaurant(restaurant);
      results.push({
        restaurantId: restaurant.id,
        deployed: statusResult.deployed,
        version: statusResult.version,
        lastModified: statusResult.lastModified,
        error: statusResult.error
      });
    } catch (error) {
      console.error(`Error checking deployment on ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        deployed: false,
        error: error.message
      });
    }
  }

  return results;
});

/**
 * Check deployment status on a single restaurant
 */
async function checkDeploymentOnRestaurant(restaurant) {
  return new Promise((resolve) => {
    const command = `powershell -Command "Test-Path '\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools\\option1_query.bat'"`;

    exec(command, { timeout: 15000 }, (error, stdout, stderr) => {
      if (error) {
        resolve({
          deployed: false,
          error: error.message
        });
      } else {
        const deployed = stdout.trim().toLowerCase() === 'true';
        resolve({
          deployed: deployed,
          version: deployed ? '1.0.0' : null,
          lastModified: deployed ? new Date().toISOString() : null
        });
      }
    });
  });
}

/**
 * Remove deployments from restaurants
 */
ipcMain.handle('remove-deployments', async (event, restaurants) => {
  const results = [];

  for (const restaurant of restaurants) {
    try {
      const removeResult = await removeDeploymentFromRestaurant(restaurant);
      results.push({
        restaurantId: restaurant.id,
        success: removeResult.success,
        error: removeResult.error
      });
    } catch (error) {
      console.error(`Error removing deployment from ${restaurant.name}:`, error);
      results.push({
        restaurantId: restaurant.id,
        success: false,
        error: error.message
      });
    }
  }

  return results;
});

/**
 * Remove deployment from a single restaurant
 */
async function removeDeploymentFromRestaurant(restaurant) {
  return new Promise((resolve) => {
    const command = `powershell -Command "Remove-Item '\\\\${restaurant.ip}\\D$\\Newpos61\\Support_Tools\\Ultimate_support_tools\\option1_query.bat' -Force -ErrorAction SilentlyContinue"`;

    exec(command, { timeout: 15000 }, (error, stdout, stderr) => {
      if (error) {
        resolve({
          success: false,
          error: error.message
        });
      } else {
        resolve({
          success: true
        });
      }
    });
  });
}