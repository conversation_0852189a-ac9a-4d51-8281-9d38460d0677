{"version": 3, "file": "email-matcher.js", "sourceRoot": "", "sources": ["../../../src/matcher/email-matcher.ts"], "names": [], "mappings": ";;;;AAAA,qCAAoC;AACpC,0CAAiF;AACjF,oDAAkD;AAElD,kCAAmD;AACnD,yCAAuC;AAEvC,wDAAwD;AACxD,oCAAoC;AAEpC,8EAA8E;AAC9E,8EAA8E;AAC9E,wEAAwE;AACxE,4GAA4G;AAC5G,gFAAgF;AAChF,IAAM,kBAAkB,GAAG,IAAI,MAAM,CAAC,WAAI,wCAA4B,yBAAuB,CAAC,CAAC;AAC/F,IAAM,cAAc,GAAG,IAAI,MAAM,CAAC,WAAI,oBAAQ,CAAC,MAAM,MAAG,CAAC,CAAC;AAE1D;;;;;;;GAOG;AACH;IAAkC,6CAAO;IAAzC;QAAA,qEAuSC;QAtSG;;;WAGG;QACO,wBAAkB,GAAG,kBAAkB,CAAC;QAElD;;;WAGG;QACO,oBAAc,GAAG,cAAc,CAAC;;IA4R9C,CAAC;IA1RG;;OAEG;IACH,mCAAY,GAAZ,UAAa,IAAY;QACrB,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAC9B,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAC5C,cAAc,GAAG,IAAI,CAAC,cAAc,EACpC,OAAO,GAAY,EAAE,EACrB,GAAG,GAAG,IAAI,CAAC,MAAM,EACjB,mBAAmB,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAElD,kCAAkC;QAClC,IAAM,iBAAiB,GAAG;YACtB,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,GAAG;YACN,CAAC,EAAE,GAAG;SACT,CAAC;QAEF,IAAI,OAAO,GAAG,CAAC,EACX,KAAK,GAAG,qBAA4B,EACpC,iBAAiB,GAAG,mBAAmB,CAAC;QAE5C,wDAAwD;QACxD,gCAAgC;QAChC,8FAA8F;QAC9F,OAAO;QAEP,OAAO,OAAO,GAAG,GAAG,EAAE;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAElC,wDAAwD;YACxD,cAAc;YACd,yGAAyG;YACzG,KAAK;YAEL,QAAQ,KAAK,EAAE;gBACX;oBACI,oBAAoB,CAAC,IAAI,CAAC,CAAC;oBAC3B,MAAM;gBAEV;oBACI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,CAAe,EAAE,IAAI,CAAC,CAAC;oBAC1D,MAAM;gBACV;oBACI,cAAc,CAAC,IAAI,CAAC,CAAC;oBACrB,MAAM;gBACV;oBACI,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACxB,MAAM;gBACV;oBACI,WAAW,CAAC,IAAI,CAAC,CAAC;oBAClB,MAAM;gBACV;oBACI,eAAe,CAAC,IAAI,CAAC,CAAC;oBACtB,MAAM;gBACV;oBACI,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACxB,MAAM;gBACV;oBACI,cAAc,CAAC,IAAI,CAAC,CAAC;oBACrB,MAAM;gBAEV;oBACI,IAAA,+BAAuB,EAAC,KAAK,CAAC,CAAC;aACtC;YAED,wDAAwD;YACxD,cAAc;YACd,yGAAyG;YACzG,KAAK;YAEL,OAAO,EAAE,CAAC;SACb;QAED,mDAAmD;QACnD,2BAA2B,EAAE,CAAC;QAE9B,wDAAwD;QACxD,yCAAyC;QAEzC,OAAO,OAAO,CAAC;QAEf,uDAAuD;QACvD,SAAS,oBAAoB,CAAC,IAAY;YACtC,IAAI,IAAI,KAAK,GAAG,EAAE;gBACd,eAAe,gBAAc,CAAC;aACjC;iBAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtC,eAAe,EAAE,CAAC;aACrB;iBAAM;gBACH,2CAA2C;aAC9C;QACL,CAAC;QAED,4DAA4D;QAC5D,SAAS,WAAW,CAAC,QAAoB,EAAE,IAAY;YACnD,IAAI,QAAQ,KAAK,GAAG,EAAE;gBAClB,gDAAgD;gBAChD,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC/B,KAAK,oBAAkB,CAAC;oBACxB,iBAAiB,GAAG,IAAI,iBAAiB,iDAClC,iBAAiB,KACpB,eAAe,EAAE,IAAI,IACvB,CAAC;iBACN;qBAAM;oBACH,6DAA6D;oBAC7D,wDAAwD;oBACxD,+DAA+D;oBAC/D,yBAAyB,EAAE,CAAC;iBAC/B;aACJ;iBAAM,IAAI,iBAAiB,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;gBAC7C,wDAAwD;gBACxD,eAAe;aAClB;iBAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtC,4DAA4D;gBAC5D,sDAAsD;gBACtD,KAAK,oBAAkB,CAAC;aAC3B;iBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;gBACrB,4DAA4D;gBAC5D,gBAAgB;gBAChB,KAAK,uBAAqB,CAAC;aAC9B;iBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;gBACrB,4DAA4D;gBAC5D,iBAAiB;gBACjB,KAAK,iBAAe,CAAC;aACxB;iBAAM;gBACH,oEAAoE;gBACpE,yBAAyB,EAAE,CAAC;aAC/B;QACL,CAAC;QAED,mEAAmE;QACnE,kDAAkD;QAClD,SAAS,cAAc,CAAC,IAAY;YAChC,IAAI,IAAI,KAAK,GAAG,EAAE;gBACd,KAAK,uBAAqB,CAAC;aAC9B;iBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;gBACrB,KAAK,iBAAe,CAAC;aACxB;iBAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtC,gDAAgD;aACnD;iBAAM;gBACH,oEAAoE;gBACpE,yBAAyB,EAAE,CAAC;aAC/B;QACL,CAAC;QAED,qCAAqC;QACrC,SAAS,iBAAiB,CAAC,IAAY;YACnC,IAAI,IAAI,KAAK,GAAG,EAAE;gBACd,2DAA2D;gBAC3D,aAAa;gBACb,yBAAyB,EAAE,CAAC;aAC/B;iBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;gBACrB,+DAA+D;gBAC/D,mBAAmB;gBACnB,yBAAyB,EAAE,CAAC;aAC/B;iBAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACtC,KAAK,oBAAkB,CAAC;aAC3B;iBAAM;gBACH,sCAAsC;gBACtC,yBAAyB,EAAE,CAAC;aAC/B;QACL,CAAC;QAED,SAAS,WAAW,CAAC,IAAY;YAC7B,IAAI,+BAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAChC,KAAK,qBAAmB,CAAC;aAC5B;iBAAM;gBACH,sCAAsC;gBACtC,yBAAyB,EAAE,CAAC;aAC/B;QACL,CAAC;QAED,SAAS,eAAe,CAAC,IAAY;YACjC,IAAI,IAAI,KAAK,GAAG,EAAE;gBACd,KAAK,oBAAkB,CAAC;aAC3B;iBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;gBACrB,KAAK,uBAAqB,CAAC;aAC9B;iBAAM,IAAI,+BAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACvC,+BAA+B;aAClC;iBAAM;gBACH,4DAA4D;gBAC5D,WAAW;gBACX,2BAA2B,EAAE,CAAC;aACjC;QACL,CAAC;QAED,SAAS,iBAAiB,CAAC,IAAY;YACnC,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,EAAE;gBAC9B,2DAA2D;gBAC3D,2BAA2B,EAAE,CAAC;aACjC;iBAAM,IAAI,+BAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACvC,KAAK,qBAAmB,CAAC;aAC5B;iBAAM;gBACH,gBAAgB;gBAChB,2BAA2B,EAAE,CAAC;aACjC;QACL,CAAC;QAED,SAAS,cAAc,CAAC,IAAY;YAChC,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,EAAE;gBAC9B,wDAAwD;gBACxD,2BAA2B,EAAE,CAAC;aACjC;iBAAM,IAAI,+BAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACvC,KAAK,qBAAmB,CAAC;gBAEzB,6DAA6D;gBAC7D,8DAA8D;gBAC9D,4DAA4D;gBAC5D,gEAAgE;gBAChE,iBAAiB,GAAG,IAAI,iBAAiB,iDAClC,iBAAiB,KACpB,YAAY,EAAE,IAAI,IACpB,CAAC;aACN;iBAAM;gBACH,gBAAgB;gBAChB,2BAA2B,EAAE,CAAC;aACjC;QACL,CAAC;QAED,SAAS,eAAe,CAAC,QAA0B;YAA1B,yBAAA,EAAA,4BAA0B;YAC/C,KAAK,GAAG,QAAQ,CAAC;YACjB,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,SAAS,yBAAyB;YAC9B,KAAK,wBAAsB,CAAC;YAC5B,iBAAiB,GAAG,mBAAmB,CAAC;QAC5C,CAAC;QAED;;;WAGG;QACH,SAAS,2BAA2B;YAChC,IAAI,iBAAiB,CAAC,YAAY,EAAE;gBAChC,gFAAgF;gBAChF,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBAE7D,4DAA4D;gBAC5D,8DAA8D;gBAC9D,4DAA4D;gBAC5D,aAAa;gBACb,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;oBAC3B,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBAC1C;gBAED,IAAM,YAAY,GAAG,iBAAiB,CAAC,eAAe;oBAClD,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC;oBACrC,CAAC,CAAC,WAAW,CAAC;gBAElB,sEAAsE;gBACtE,IAAI,qBAAqB,CAAC,YAAY,CAAC,EAAE;oBACrC,OAAO,CAAC,IAAI,CACR,IAAI,wBAAU,CAAC;wBACX,UAAU,EAAE,UAAU;wBACtB,WAAW,EAAE,WAAW;wBACxB,MAAM,EAAE,iBAAiB,CAAC,GAAG;wBAC7B,KAAK,EAAE,YAAY;qBACtB,CAAC,CACL,CAAC;iBACL;aACJ;YAED,yBAAyB,EAAE,CAAC;YAE5B;;;;eAIG;YACH,SAAS,qBAAqB,CAAC,YAAoB;gBAC/C,IAAM,eAAe,GAAW,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;gBACpE,IAAM,sBAAsB,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;gBAC7D,IAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAE/D,OAAO,UAAU,CAAC;YACtB,CAAC;QACL,CAAC;IACL,CAAC;IACL,mBAAC;AAAD,CAAC,AAvSD,CAAkC,iBAAO,GAuSxC;AAvSY,oCAAY;AAuTzB;IAKI,2BAAY,GAAoC;QAApC,oBAAA,EAAA,QAAoC;QAC5C,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC;IAC3C,CAAC;IACL,wBAAC;AAAD,CAAC,AAVD,IAUC"}