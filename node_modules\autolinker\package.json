{"name": "autolinker", "version": "3.16.2", "description": "Utility to automatically link the URLs, email addresses, phone numbers, hashtags, and mentions (Twitter, Instagram) in a given block of text/HTML", "main": "./dist/commonjs/index.js", "typings": "./dist/commonjs/index.d.ts", "module": "./dist/es2015/index.js", "files": ["dist"], "scripts": {"build": "npm-run-all clean update-tld-regex build:src build:docs build:live-example", "build:docs": "ts-node scripts/build-docs.ts", "build:src": "ts-node scripts/build.ts", "build:live-example": "webpack", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm-run-all build test", "start": "webpack serve --open", "test": "npm-run-all test:unit test:integration", "test:unit": "node --require=ts-node/register node_modules/jasmine/bin/jasmine.js \"tests/**/*.spec.ts\"", "test:integration": "ts-node scripts/test-integration.ts", "update-tld-regex": "ts-node scripts/update-tld-regex.ts", "prepare": "husky install"}, "repository": {"type": "git", "url": "git://github.com/gregjacobs/Autolinker.js.git"}, "keywords": ["auto", "link", "autolink", "url", "urls", "anchor"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/gregjacobs/Autolinker.js/issues"}, "homepage": "https://github.com/gregjacobs/Autolinker.js", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^21.0.0", "@rollup/plugin-node-resolve": "^13.1.3", "@types/dedent": "^0.7.0", "@types/fs-extra": "^9.0.13", "@types/jasmine": "^3.10.3", "@types/jquery": "^3.5.5", "@types/lodash": "^4.14.170", "@types/node": "^16.11.26", "@types/webpack": "^5.28.0", "axios": "^0.26.0", "css-loader": "^6.6.0", "dedent": "^0.7.0", "fast-glob": "^3.2.11", "fs-extra": "^10.0.1", "html-webpack-plugin": "^5.5.0", "husky": "^7.0.4", "jasmine": "^4.0.2", "jsduck": "^1.1.2", "lint-staged": "^12.3.4", "lodash": "^4.17.21", "mkdirp": "^1.0.4", "npm": "^8.1.0", "npm-run-all": "^4.1.5", "prettier": "2.5.1", "punycode": "^2.1.1", "puppeteer": "^13.4.0", "rimraf": "^3.0.2", "rollup": "^2.52.2", "style-loader": "^3.3.1", "terser": "^5.11.0", "ts-loader": "^9.2.3", "ts-node": "^10.5.0", "typescript": "^4.3.4", "webpack": "^5.69.1", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4"}}