{"version": 3, "file": "hashtag-matcher.js", "sourceRoot": "", "sources": ["../../../src/matcher/hashtag-matcher.ts"], "names": [], "mappings": ";;;;AAAA,qCAAmD;AACnD,0CAAwF;AACxF,wDAAsD;AAEtD,kCAAmD;AAEnD,wDAAwD;AACxD,oCAAoC;AAEpC,IAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,YAAK,wCAA4B,MAAG,CAAC,CAAC;AAE3E;;;;;GAKG;AACH;IAAoC,+CAAO;IAcvC;;;;OAIG;IACH,wBAAY,GAAyB;QAArC,YACI,kBAAM,GAAG,CAAC,SAGb;QAtBD;;;;;;;;;;WAUG;QACgB,iBAAW,GAAmB,SAAS,CAAC,CAAC,gGAAgG;QAUxJ,KAAI,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;;IACvC,CAAC;IAED;;OAEG;IACH,qCAAY,GAAZ,UAAa,IAAY;QACrB,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAErC,IAAM,OAAO,GAAY,EAAE,CAAC;QAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;QAExB,IAAI,OAAO,GAAG,CAAC,EACX,WAAW,GAAG,CAAC,CAAC,EAChB,KAAK,GAAG,YAAmB,CAAC;QAEhC,wDAAwD;QACxD,gCAAgC;QAChC,8FAA8F;QAC9F,OAAO;QAEP,OAAO,OAAO,GAAG,GAAG,EAAE;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAElC,wDAAwD;YACxD,cAAc;YACd,yGAAyG;YACzG,KAAK;YAEL,QAAQ,KAAK,EAAE;gBACX;oBACI,SAAS,CAAC,IAAI,CAAC,CAAC;oBAChB,MAAM;gBACV;oBACI,uBAAuB,CAAC,IAAI,CAAC,CAAC;oBAC9B,MAAM;gBACV;oBACI,oBAAoB,CAAC,IAAI,CAAC,CAAC;oBAC3B,MAAM;gBACV;oBACI,oBAAoB,CAAC,IAAI,CAAC,CAAC;oBAC3B,MAAM;gBAEV;oBACI,IAAA,+BAAuB,EAAC,KAAK,CAAC,CAAC;aACtC;YAED,wDAAwD;YACxD,cAAc;YACd,yGAAyG;YACzG,KAAK;YAEL,OAAO,EAAE,CAAC;SACb;QAED,mDAAmD;QACnD,mBAAmB,EAAE,CAAC;QAEtB,wDAAwD;QACxD,yCAAyC;QAEzC,OAAO,OAAO,CAAC;QAEf,4DAA4D;QAC5D,SAAS,SAAS,CAAC,IAAY;YAC3B,IAAI,IAAI,KAAK,GAAG,EAAE;gBACd,KAAK,0BAAwB,CAAC;gBAC9B,WAAW,GAAG,OAAO,CAAC;aACzB;iBAAM,IAAI,sCAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC9C,KAAK,6BAA2B,CAAC;aACpC;iBAAM;gBACH,iEAAiE;aACpE;QACL,CAAC;QAED,wEAAwE;QACxE,iEAAiE;QACjE,sEAAsE;QACtE,0DAA0D;QAC1D,SAAS,uBAAuB,CAAC,IAAY;YACzC,IAAI,sCAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACvC,uCAAuC;aAC1C;iBAAM;gBACH,KAAK,eAAa,CAAC;aACtB;QACL,CAAC;QAED,gEAAgE;QAChE,SAAS,oBAAoB,CAAC,IAAY;YACtC,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC9B,+CAA+C;gBAC/C,KAAK,0BAAwB,CAAC;aACjC;iBAAM,IAAI,sCAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC9C,KAAK,6BAA2B,CAAC;aACpC;iBAAM;gBACH,KAAK,eAAa,CAAC;aACtB;QACL,CAAC;QAED,sEAAsE;QACtE,SAAS,oBAAoB,CAAC,IAAY;YACtC,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC9B,uDAAuD;aAC1D;iBAAM;gBACH,mBAAmB,EAAE,CAAC;gBACtB,WAAW,GAAG,CAAC,CAAC,CAAC;gBAEjB,IAAI,sCAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACvC,KAAK,6BAA2B,CAAC;iBACpC;qBAAM;oBACH,KAAK,eAAa,CAAC;iBACtB;aACJ;QACL,CAAC;QAED;;WAEG;QACH,SAAS,mBAAmB;YACxB,IAAI,WAAW,GAAG,CAAC,CAAC,IAAI,OAAO,GAAG,WAAW,IAAI,GAAG,EAAE;gBAClD,8DAA8D;gBAC9D,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBAEnD,IAAM,KAAK,GAAG,IAAI,4BAAY,CAAC;oBAC3B,UAAU,YAAA;oBACV,WAAW,EAAE,WAAW;oBACxB,MAAM,EAAE,WAAW;oBACnB,WAAW,EAAE,WAAW;oBACxB,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;iBAChC,CAAC,CAAC;gBACH,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACvB;QACL,CAAC;IACL,CAAC;IACL,qBAAC;AAAD,CAAC,AA7JD,CAAoC,iBAAO,GA6J1C;AA7JY,wCAAc;AA8Kd,QAAA,eAAe,GAAqB,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC"}