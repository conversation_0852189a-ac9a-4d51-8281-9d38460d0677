{"version": 3, "file": "parse-html.js", "sourceRoot": "", "sources": ["../../../src/htmlParser/parse-html.ts"], "names": [], "mappings": ";;;;AACA,0CAAwF;AACxF,kCAAmD;AAEnD,wDAAwD;AACxD,oCAAoC;AAEpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkDG;AACH,SAAgB,SAAS,CACrB,IAAY,EACZ,EAYC;QAXG,SAAS,eAAA,EACT,UAAU,gBAAA,EACV,MAAM,YAAA,EACN,SAAS,eAAA,EACT,SAAS,eAAA;IASb,IAAM,YAAY,GAAG,IAAI,UAAU,EAAE,CAAC;IAEtC,IAAI,OAAO,GAAG,CAAC,EACX,GAAG,GAAG,IAAI,CAAC,MAAM,EACjB,KAAK,GAAG,YAAmB,EAC3B,cAAc,GAAG,CAAC,EAAE,wCAAwC;IAC5D,UAAU,GAAG,YAAY,CAAC,CAAC,+CAA+C;IAE9E,wDAAwD;IACxD,gCAAgC;IAChC,2FAA2F;IAC3F,OAAO;IAEP,OAAO,OAAO,GAAG,GAAG,EAAE;QAClB,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEhC,wDAAwD;QACxD,iEAAiE;QACjE,cAAc;QACd,mHAAmH;QACnH,KAAK;QAEL,QAAQ,KAAK,EAAE;YACX;gBACI,SAAS,CAAC,IAAI,CAAC,CAAC;gBAChB,MAAM;YACV;gBACI,YAAY,CAAC,IAAI,CAAC,CAAC;gBACnB,MAAM;YACV;gBACI,eAAe,CAAC,IAAI,CAAC,CAAC;gBACtB,MAAM;YACV;gBACI,YAAY,CAAC,IAAI,CAAC,CAAC;gBACnB,MAAM;YACV;gBACI,wBAAwB,CAAC,IAAI,CAAC,CAAC;gBAC/B,MAAM;YACV;gBACI,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBACzB,MAAM;YACV;gBACI,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBAC9B,MAAM;YACV;gBACI,yBAAyB,CAAC,IAAI,CAAC,CAAC;gBAChC,MAAM;YACV;gBACI,+BAA+B,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM;YACV;gBACI,+BAA+B,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM;YACV;gBACI,2BAA2B,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM;YACV;gBACI,8BAA8B,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM;YACV;gBACI,wBAAwB,CAAC,IAAI,CAAC,CAAC;gBAC/B,MAAM;YACV;gBACI,0BAA0B,CAAC,IAAI,CAAC,CAAC;gBACjC,MAAM;YACV;gBACI,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACxB,MAAM;YACV;gBACI,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBAC5B,MAAM;YACV;gBACI,YAAY,CAAC,IAAI,CAAC,CAAC;gBACnB,MAAM;YACV;gBACI,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM;YACV;gBACI,eAAe,CAAC,IAAI,CAAC,CAAC;gBACtB,MAAM;YACV;gBACI,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC1B,MAAM;YACV;gBACI,YAAY,CAAC,IAAI,CAAC,CAAC;gBACnB,MAAM;YAEV;gBACI,IAAA,+BAAuB,EAAC,KAAK,CAAC,CAAC;SACtC;QAED,wDAAwD;QACxD,iEAAiE;QACjE,cAAc;QACd,mHAAmH;QACnH,KAAK;QAEL,OAAO,EAAE,CAAC;KACb;IAED,IAAI,cAAc,GAAG,OAAO,EAAE;QAC1B,QAAQ,EAAE,CAAC;KACd;IAED,wDAAwD;IACxD,0CAA0C;IAE1C,uEAAuE;IACvE,sDAAsD;IACtD,SAAS,SAAS,CAAC,IAAY;QAC3B,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,WAAW,EAAE,CAAC;SACjB;IACL,CAAC;IAED,iDAAiD;IACjD,0DAA0D;IAC1D,SAAS,YAAY,CAAC,IAAY;QAC9B,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,KAAK,sCAAmC,CAAC;SAC5C;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,KAAK,qBAAmB,CAAC;YACzB,UAAU,GAAG,IAAI,UAAU,iDAAM,UAAU,KAAE,SAAS,EAAE,IAAI,IAAG,CAAC;SACnE;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,6DAA6D;YAC7D,WAAW,EAAE,CAAC;SACjB;aAAM,IAAI,oBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC5B,mCAAmC;YACnC,KAAK,kBAAgB,CAAC;YACtB,UAAU,GAAG,IAAI,UAAU,iDAAM,UAAU,KAAE,SAAS,EAAE,IAAI,IAAG,CAAC;SACnE;aAAM;YACH,YAAY;YACZ,KAAK,eAAa,CAAC;YACnB,UAAU,GAAG,YAAY,CAAC;SAC7B;IACL,CAAC;IAED,0EAA0E;IAC1E,2CAA2C;IAC3C,0DAA0D;IAC1D,SAAS,YAAY,CAAC,IAAY;QAC9B,IAAI,wBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzB,UAAU,GAAG,IAAI,UAAU,iDACpB,UAAU,KACb,IAAI,EAAE,cAAc,EAAE,IACxB,CAAC;YACH,KAAK,8BAA4B,CAAC;SACrC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,6DAA6D;YAC7D,WAAW,EAAE,CAAC;SACjB;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,UAAU,GAAG,IAAI,UAAU,iDACpB,UAAU,KACb,IAAI,EAAE,cAAc,EAAE,IACxB,CAAC;YACH,KAAK,+BAA4B,CAAC;SACrC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,UAAU,GAAG,IAAI,UAAU,iDACpB,UAAU,KACb,IAAI,EAAE,cAAc,EAAE,IACxB,CAAC;YACH,0BAA0B,EAAE,CAAC,CAAC,+BAA+B;SAChE;aAAM,IAAI,CAAC,oBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,EAAE;YACpE,gEAAgE;YAChE,gDAAgD;YAChD,gBAAgB,EAAE,CAAC;SACtB;aAAM;YACH,4BAA4B;SAC/B;IACL,CAAC;IAED,oDAAoD;IACpD,8DAA8D;IAC9D,SAAS,eAAe,CAAC,IAAY;QACjC,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,oEAAoE;YACpE,gBAAgB,EAAE,CAAC;SACtB;aAAM,IAAI,oBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC5B,KAAK,kBAAgB,CAAC;SACzB;aAAM;YACH,+DAA+D;YAC/D,gBAAgB,EAAE,CAAC;SACtB;IACL,CAAC;IAED,uEAAuE;IACvE,SAAS,wBAAwB,CAAC,IAAY;QAC1C,IAAI,wBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzB,6DAA6D;SAChE;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,KAAK,+BAA4B,CAAC;SACrC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,0BAA0B,EAAE,CAAC,CAAC,+BAA+B;SAChE;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,6DAA6D;YAC7D,WAAW,EAAE,CAAC;SACjB;aAAM,IAAI,IAAI,KAAK,GAAG,IAAI,mBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,0BAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACxE,kEAAkE;YAClE,mEAAmE;YACnE,iCAAiC;YACjC,gBAAgB,EAAE,CAAC;SACtB;aAAM;YACH,gDAAgD;YAChD,KAAK,wBAAsB,CAAC;SAC/B;IACL,CAAC;IAED,gEAAgE;IAChE,SAAS,kBAAkB,CAAC,IAAY;QACpC,IAAI,wBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzB,KAAK,6BAA2B,CAAC;SACpC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,KAAK,+BAA4B,CAAC;SACrC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,KAAK,+BAA6B,CAAC;SACtC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,0BAA0B,EAAE,CAAC,CAAC,+BAA+B;SAChE;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,6DAA6D;YAC7D,WAAW,EAAE,CAAC;SACjB;aAAM,IAAI,mBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3B,kEAAkE;YAClE,mEAAmE;YACnE,iCAAiC;YACjC,gBAAgB,EAAE,CAAC;SACtB;aAAM;YACH,iDAAiD;SACpD;IACL,CAAC;IAED,sEAAsE;IACtE,SAAS,uBAAuB,CAAC,IAAY;QACzC,IAAI,wBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzB,0CAA0C;SAC7C;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,KAAK,+BAA4B,CAAC;SACrC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,KAAK,+BAA6B,CAAC;SACtC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,0BAA0B,EAAE,CAAC;SAChC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,6DAA6D;YAC7D,WAAW,EAAE,CAAC;SACjB;aAAM,IAAI,mBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3B,kEAAkE;YAClE,mEAAmE;YACnE,iCAAiC;YACjC,gBAAgB,EAAE,CAAC;SACtB;aAAM;YACH,gEAAgE;YAChE,KAAK,wBAAsB,CAAC;SAC/B;IACL,CAAC;IAED,wEAAwE;IACxE,SAAS,yBAAyB,CAAC,IAAY;QAC3C,IAAI,wBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzB,0CAA0C;SAC7C;aAAM,IAAI,IAAI,KAAK,IAAG,EAAE;YACrB,KAAK,qCAAmC,CAAC;SAC5C;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,KAAK,qCAAmC,CAAC;SAC5C;aAAM,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC3B,iEAAiE;YACjE,iCAAiC;YACjC,gBAAgB,EAAE,CAAC;SACtB;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,6DAA6D;YAC7D,WAAW,EAAE,CAAC;SACjB;aAAM;YACH,+DAA+D;YAC/D,KAAK,kCAA+B,CAAC;SACxC;IACL,CAAC;IAED,+EAA+E;IAC/E,SAAS,+BAA+B,CAAC,IAAY;QACjD,IAAI,IAAI,KAAK,IAAG,EAAE;YACd,0CAA0C;YAC1C,KAAK,qCAAkC,CAAC;SAC3C;aAAM;YACH,qEAAqE;SACxE;IACL,CAAC;IAED,+EAA+E;IAC/E,SAAS,+BAA+B,CAAC,IAAY;QACjD,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,0CAA0C;YAC1C,KAAK,qCAAkC,CAAC;SAC3C;aAAM;YACH,qEAAqE;SACxE;IACL,CAAC;IAED,0EAA0E;IAC1E,SAAS,2BAA2B,CAAC,IAAY;QAC7C,IAAI,wBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzB,KAAK,8BAA4B,CAAC;SACrC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,0BAA0B,EAAE,CAAC;SAChC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,6DAA6D;YAC7D,WAAW,EAAE,CAAC;SACjB;aAAM;YACH,+DAA+D;SAClE;IACL,CAAC;IAED,8EAA8E;IAC9E,SAAS,8BAA8B,CAAC,IAAY;QAChD,IAAI,wBAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACzB,KAAK,8BAA4B,CAAC;SACrC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,KAAK,+BAA4B,CAAC;SACrC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,0BAA0B,EAAE,CAAC;SAChC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,6DAA6D;YAC7D,WAAW,EAAE,CAAC;SACjB;aAAM;YACH,iEAAiE;YACjE,kEAAkE;YAClE,oCAAoC;YACpC,KAAK,8BAA4B,CAAC;YAClC,yBAAyB,EAAE,CAAC;SAC/B;IACL,CAAC;IAED,yEAAyE;IACzE,kCAAkC;IAClC,wEAAwE;IACxE,SAAS,wBAAwB,CAAC,IAAY;QAC1C,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,UAAU,GAAG,IAAI,UAAU,iDAAM,UAAU,KAAE,SAAS,EAAE,IAAI,IAAG,CAAC;YAChE,0BAA0B,EAAE,CAAC,CAAC,+BAA+B;SAChE;aAAM;YACH,KAAK,8BAA4B,CAAC;SACrC;IACL,CAAC;IAED,yEAAyE;IACzE,8BAA8B;IAC9B,SAAS,0BAA0B,CAAC,IAAY;QAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;YAClC,eAAe;YACf,OAAO,IAAI,CAAC,CAAC,CAAC,uBAAuB;YACrC,UAAU,GAAG,IAAI,UAAU,iDAAM,UAAU,KAAE,IAAI,EAAE,SAAS,IAAG,CAAC;YAChE,KAAK,wBAAqB,CAAC;SAC9B;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,SAAS,EAAE;YAC5D,OAAO,IAAI,CAAC,CAAC,CAAC,uBAAuB;YACrC,UAAU,GAAG,IAAI,UAAU,iDAAM,UAAU,KAAE,IAAI,EAAE,SAAS,IAAG,CAAC;YAChE,KAAK,mBAAgB,CAAC;SACzB;aAAM;YACH,kEAAkE;YAClE,kEAAkE;YAClE,iEAAiE;YACjE,mEAAmE;YACnE,mDAAmD;YACnD,gBAAgB,EAAE,CAAC;SACtB;IACL,CAAC;IAED,kDAAkD;IAClD,+DAA+D;IAC/D,SAAS,iBAAiB,CAAC,IAAY;QACnC,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,2DAA2D;YAC3D,KAAK,4BAAyB,CAAC;SAClC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,gEAAgE;YAChE,gEAAgE;YAChE,UAAU;YACV,gBAAgB,EAAE,CAAC;SACtB;aAAM;YACH,iDAAiD;YACjD,KAAK,mBAAgB,CAAC;SACzB;IACL,CAAC;IAED,2DAA2D;IAC3D,oEAAoE;IACpE,SAAS,qBAAqB,CAAC,IAAY;QACvC,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,+CAA+C;YAC/C,KAAK,sBAAmB,CAAC;SAC5B;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,gEAAgE;YAChE,gEAAgE;YAChE,WAAW;YACX,gBAAgB,EAAE,CAAC;SACtB;aAAM;YACH,4CAA4C;YAC5C,KAAK,mBAAgB,CAAC;SACzB;IACL,CAAC;IAED,8CAA8C;IAC9C,yDAAyD;IACzD,SAAS,YAAY,CAAC,IAAY;QAC9B,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,KAAK,0BAAuB,CAAC;SAChC;aAAM;YACH,iDAAiD;SACpD;IACL,CAAC;IAED,wEAAwE;IACxE,6CAA6C;IAC7C,kEAAkE;IAClE,SAAS,mBAAmB,CAAC,IAAY;QACrC,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,KAAK,sBAAmB,CAAC;SAC5B;aAAM;YACH,mDAAmD;YACnD,KAAK,mBAAgB,CAAC;SACzB;IACL,CAAC;IAED,yEAAyE;IACzE,yCAAyC;IACzC,6DAA6D;IAC7D,SAAS,eAAe,CAAC,IAAY;QACjC,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,0BAA0B,EAAE,CAAC;SAChC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,KAAK,0BAAuB,CAAC;SAChC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,wDAAwD;SAC3D;aAAM;YACH,kEAAkE;YAClE,oDAAoD;YACpD,KAAK,mBAAgB,CAAC;SACzB;IACL,CAAC;IAED,oDAAoD;IACpD,kEAAkE;IAClE,SAAS,mBAAmB,CAAC,IAAY;QACrC,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,iEAAiE;YACjE,mDAAmD;YACnD,KAAK,0BAAuB,CAAC;SAChC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,0CAA0C;YAC1C,0BAA0B,EAAE,CAAC;SAChC;aAAM;YACH,4DAA4D;YAC5D,iBAAiB;YACjB,KAAK,mBAAgB,CAAC;SACzB;IACL,CAAC;IAED;;;;;;;;;OASG;IACH,SAAS,YAAY,CAAC,IAAY;QAC9B,IAAI,IAAI,KAAK,GAAG,EAAE;YACd,0BAA0B,EAAE,CAAC;SAChC;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACrB,WAAW,EAAE,CAAC;SACjB;aAAM;YACH,4BAA4B;SAC/B;IACL,CAAC;IAED;;;;;;OAMG;IACH,SAAS,gBAAgB;QACrB,KAAK,eAAa,CAAC;QACnB,UAAU,GAAG,YAAY,CAAC;IAC9B,CAAC;IAED;;;;;;;OAOG;IACH,SAAS,WAAW;QAChB,KAAK,kBAAgB,CAAC;QACtB,UAAU,GAAG,IAAI,UAAU,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,SAAS,0BAA0B;QAC/B,IAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;QACjE,IAAI,aAAa,EAAE;YACf,gEAAgE;YAChE,mEAAmE;YACnE,OAAO;YACP,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;SACzC;QAED,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE;YAC/B,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE;YACtC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SAC7B;aAAM;YACH,IAAI,UAAU,CAAC,SAAS,EAAE;gBACtB,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;aAC9C;YACD,IAAI,UAAU,CAAC,SAAS,EAAE;gBACtB,6DAA6D;gBAC7D,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;aAC/C;SACJ;QAED,yEAAyE;QACzE,gBAAgB,EAAE,CAAC;QACnB,cAAc,GAAG,OAAO,GAAG,CAAC,CAAC;IACjC,CAAC;IAED,SAAS,QAAQ;QACb,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QACjD,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;QAE7B,cAAc,GAAG,OAAO,GAAG,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,SAAS,cAAc;QACnB,IAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IACvD,CAAC;IAED;;;;OAIG;IACH,SAAS,yBAAyB;QAC9B,OAAO,EAAE,CAAC;IACd,CAAC;AACL,CAAC;AA1jBD,8BA0jBC;AAED;IAOI,oBAAY,GAA6B;QAA7B,oBAAA,EAAA,QAA6B;QACrC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC;IACrC,CAAC;IACL,iBAAC;AAAD,CAAC,AAdD,IAcC"}